'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { ReportService } from '@/lib/api/reports'
import type { 
  Report, 
  CreateReportRequest, 
  ReportFilters, 
  ReportStats 
} from '@/types/report'
import type { Keyword } from '@/types/keyword'

export function useReports(filters?: ReportFilters) {
  const { user } = useAuth()
  const [reports, setReports] = useState<Report[]>([])
  const [stats, setStats] = useState<ReportStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchReports = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      setError(null)
      const data = await ReportService.getReports(user.id, filters)
      setReports(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取报告失败')
    } finally {
      setLoading(false)
    }
  }, [user?.id, filters])

  const fetchStats = useCallback(async () => {
    if (!user?.id) return

    try {
      const statsData = await ReportService.getReportStats(user.id)
      setStats(statsData)
    } catch (err) {
      console.error('获取报告统计失败:', err)
    }
  }, [user?.id])

  const createReport = async (keywords: Keyword[], reportData: CreateReportRequest) => {
    if (!user?.id) throw new Error('用户未登录')

    try {
      const newReport = await ReportService.createReport(user.id, keywords, reportData)
      setReports(prev => [newReport, ...prev])
      await fetchStats() // 更新统计数据
      
      // 启动轮询检查报告状态
      pollReportStatus(newReport.id)
      
      return newReport
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建报告失败'
      setError(errorMessage)
      throw err
    }
  }

  const deleteReport = async (reportId: string) => {
    try {
      await ReportService.deleteReport(reportId)
      setReports(prev => prev.filter(report => report.id !== reportId))
      await fetchStats() // 更新统计数据
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除报告失败'
      setError(errorMessage)
      throw err
    }
  }

  const getReport = async (reportId: string): Promise<Report | null> => {
    try {
      return await ReportService.getReport(reportId)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取报告详情失败'
      setError(errorMessage)
      throw err
    }
  }

  // 轮询检查报告生成状态
  const pollReportStatus = useCallback(async (reportId: string) => {
    const maxAttempts = 30 // 最多轮询30次（约5分钟）
    let attempts = 0
    
    const poll = async () => {
      try {
        const report = await ReportService.getReport(reportId)
        if (report) {
          // 更新报告列表中的状态
          setReports(prev => prev.map(r => r.id === reportId ? report : r))
          
          // 如果报告完成或失败，停止轮询
          if (report.status === 'completed' || report.status === 'failed') {
            await fetchStats() // 更新统计数据
            return
          }
          
          // 如果还在生成中且未超过最大尝试次数，继续轮询
          if (report.status === 'generating' && attempts < maxAttempts) {
            attempts++
            setTimeout(poll, 10000) // 10秒后再次检查
          }
        }
      } catch (err) {
        console.error('轮询报告状态失败:', err)
      }
    }
    
    // 延迟开始轮询，给服务器一些处理时间
    setTimeout(poll, 5000)
  }, [fetchStats])

  const addSampleReports = async (keywords: Keyword[]) => {
    if (!user?.id) throw new Error('用户未登录')

    try {
      await ReportService.addSampleReports(user.id, keywords)
      await fetchReports() // 刷新报告列表
      await fetchStats() // 更新统计数据
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '添加示例报告失败'
      setError(errorMessage)
      throw err
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchReports()
      fetchStats()
    }
  }, [user?.id, fetchReports, fetchStats])

  // 计算衍生状态
  const pendingReports = reports.filter(r => r.status === 'generating').length
  const completedReports = reports.filter(r => r.status === 'completed')
  const failedReports = reports.filter(r => r.status === 'failed')
  const recentReports = reports.slice(0, 5) // 最近5个报告

  return {
    reports,
    stats,
    loading,
    error,
    pendingReports,
    completedReports,
    failedReports,
    recentReports,
    createReport,
    deleteReport,
    getReport,
    addSampleReports,
    refetch: fetchReports,
    refreshStats: fetchStats,
  }
}
