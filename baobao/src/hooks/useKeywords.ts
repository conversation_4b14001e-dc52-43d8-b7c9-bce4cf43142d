'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { KeywordService } from '@/lib/api/keywords'
import type { Keyword, CreateKeywordRequest, UpdateKeywordRequest, KeywordStats } from '@/types/keyword'

export function useKeywords() {
  const { user } = useAuth()
  const [keywords, setKeywords] = useState<Keyword[]>([])
  const [stats, setStats] = useState<KeywordStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchKeywords = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      setError(null)
      const data = await KeywordService.getKeywords(user.id)
      setKeywords(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取关键词失败')
    } finally {
      setLoading(false)
    }
  }, [user?.id])

  const fetchStats = useCallback(async () => {
    if (!user?.id) return

    try {
      const statsData = await KeywordService.getKeywordStats(user.id)
      setStats(statsData)
    } catch (err) {
      console.error('获取统计数据失败:', err)
    }
  }, [user?.id])

  const createKeyword = async (keywordData: CreateKeywordRequest) => {
    if (!user?.id) throw new Error('用户未登录')

    try {
      const newKeyword = await KeywordService.createKeyword(user.id, keywordData)
      setKeywords(prev => [newKeyword, ...prev])
      await fetchStats() // 更新统计数据
      return newKeyword
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建关键词失败'
      setError(errorMessage)
      throw err
    }
  }

  const updateKeyword = async (keywordId: string, updates: UpdateKeywordRequest) => {
    try {
      const updatedKeyword = await KeywordService.updateKeyword(keywordId, updates)
      setKeywords(prev => 
        prev.map(keyword => 
          keyword.id === keywordId ? updatedKeyword : keyword
        )
      )
      await fetchStats() // 更新统计数据
      return updatedKeyword
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新关键词失败'
      setError(errorMessage)
      throw err
    }
  }

  const deleteKeyword = async (keywordId: string) => {
    try {
      await KeywordService.deleteKeyword(keywordId)
      setKeywords(prev => prev.filter(keyword => keyword.id !== keywordId))
      await fetchStats() // 更新统计数据
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除关键词失败'
      setError(errorMessage)
      throw err
    }
  }

  const toggleKeywordStatus = async (keywordId: string, isActive: boolean) => {
    try {
      const updatedKeyword = await KeywordService.toggleKeywordStatus(keywordId, isActive)
      setKeywords(prev => 
        prev.map(keyword => 
          keyword.id === keywordId ? updatedKeyword : keyword
        )
      )
      await fetchStats() // 更新统计数据
      return updatedKeyword
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新关键词状态失败'
      setError(errorMessage)
      throw err
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchKeywords()
      fetchStats()
    }
  }, [user?.id, fetchKeywords, fetchStats])

  return {
    keywords,
    stats,
    loading,
    error,
    createKeyword,
    updateKeyword,
    deleteKeyword,
    toggleKeywordStatus,
    refetch: fetchKeywords,
  }
}
