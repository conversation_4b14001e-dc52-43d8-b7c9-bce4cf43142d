'use client'

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { NotificationService } from '@/lib/api/notifications'
import type { 
  NotificationSettings, 
  CreateNotificationSettingsRequest, 
  UpdateNotificationSettingsRequest,
  NotificationPreview
} from '@/types/notification'

export function useNotifications() {
  const { user } = useAuth()
  const [settings, setSettings] = useState<NotificationSettings | null>(null)
  const [preview, setPreview] = useState<NotificationPreview | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSettings = useCallback(async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      setError(null)
      const data = await NotificationService.getSettings(user.id)
      setSettings(data)
      
      // 如果有设置，获取预览信息
      if (data) {
        const previewData = await NotificationService.getPreview(data)
        setPreview(previewData)
      } else {
        setPreview(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取推送设置失败')
    } finally {
      setLoading(false)
    }
  }, [user?.id])

  const createSettings = async (settingsData: CreateNotificationSettingsRequest) => {
    if (!user?.id) throw new Error('用户未登录')

    try {
      const newSettings = await NotificationService.createSettings(user.id, settingsData)
      setSettings(newSettings)
      
      // 获取预览信息
      const previewData = await NotificationService.getPreview(newSettings)
      setPreview(previewData)
      
      return newSettings
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建推送设置失败'
      setError(errorMessage)
      throw err
    }
  }

  const updateSettings = async (updates: UpdateNotificationSettingsRequest) => {
    if (!settings?.id) throw new Error('推送设置不存在')

    try {
      const updatedSettings = await NotificationService.updateSettings(settings.id, updates)
      setSettings(updatedSettings)
      
      // 更新预览信息
      const previewData = await NotificationService.getPreview(updatedSettings)
      setPreview(previewData)
      
      return updatedSettings
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新推送设置失败'
      setError(errorMessage)
      throw err
    }
  }

  const deleteSettings = async () => {
    if (!settings?.id) throw new Error('推送设置不存在')

    try {
      await NotificationService.deleteSettings(settings.id)
      setSettings(null)
      setPreview(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除推送设置失败'
      setError(errorMessage)
      throw err
    }
  }

  const toggleSettings = async (isEnabled: boolean) => {
    if (!settings?.id) throw new Error('推送设置不存在')

    try {
      const updatedSettings = await NotificationService.toggleSettings(settings.id, isEnabled)
      setSettings(updatedSettings)
      
      // 更新预览信息
      const previewData = await NotificationService.getPreview(updatedSettings)
      setPreview(previewData)
      
      return updatedSettings
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '切换推送设置失败'
      setError(errorMessage)
      throw err
    }
  }

  const refreshPreview = async () => {
    if (!settings) return

    try {
      const previewData = await NotificationService.getPreview(settings)
      setPreview(previewData)
    } catch (err) {
      console.error('刷新预览失败:', err)
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchSettings()
    }
  }, [user?.id, fetchSettings])

  return {
    settings,
    preview,
    loading,
    error,
    hasSettings: !!settings,
    isEnabled: settings?.is_enabled ?? false,
    createSettings,
    updateSettings,
    deleteSettings,
    toggleSettings,
    refreshPreview,
    refetch: fetchSettings,
  }
}
