import { generateReport } from '@/lib/ai/report-generator'
import { MockKeywordService } from '@/lib/api/keywords-mock'
import type { Keyword } from '@/types/keyword'

describe('Performance Tests', () => {
  const createMockKeywords = (count: number): Keyword[] => {
    return Array.from({ length: count }, (_, index) => ({
      id: `keyword-${index}`,
      user_id: 'test-user',
      name: `关键词 ${index + 1}`,
      description: `描述 ${index + 1}`,
      category: '科技',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))
  }

  describe('Report Generation Performance', () => {
    it('should generate report for 10 keywords within reasonable time', async () => {
      const keywords = createMockKeywords(10)
      const startTime = performance.now()
      
      const report = await generateReport(keywords, 'daily')
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(report).toBeDefined()
      expect(report.keyword_trends).toHaveLength(10)
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds
    })

    it('should handle large keyword lists efficiently', async () => {
      const keywords = createMockKeywords(50)
      const startTime = performance.now()
      
      const report = await generateReport(keywords, 'weekly')
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(report).toBeDefined()
      expect(report.keyword_trends).toHaveLength(50)
      expect(duration).toBeLessThan(15000) // Should complete within 15 seconds
    })
  })

  describe('Keyword Service Performance', () => {
    beforeEach(() => {
      // Clear localStorage before each test
      localStorage.clear()
    })

    it('should handle bulk keyword operations efficiently', async () => {
      const userId = 'test-user'
      const keywordCount = 100
      
      const startTime = performance.now()
      
      // Create multiple keywords
      const createPromises = Array.from({ length: keywordCount }, (_, index) => 
        MockKeywordService.createKeyword(userId, {
          name: `关键词 ${index + 1}`,
          description: `描述 ${index + 1}`,
          category: '科技'
        })
      )
      
      await Promise.all(createPromises)
      
      // Retrieve all keywords
      const keywords = await MockKeywordService.getKeywords(userId)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(keywords).toHaveLength(keywordCount)
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('should search through large keyword lists quickly', async () => {
      const userId = 'test-user'
      const keywords = createMockKeywords(1000)
      
      // Mock localStorage with large dataset
      localStorage.setItem('baobao_keywords', JSON.stringify(keywords))
      
      const startTime = performance.now()
      
      const result = await MockKeywordService.getKeywords(userId)
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(result).toHaveLength(1000)
      expect(duration).toBeLessThan(1000) // Should complete within 1 second
    })
  })

  describe('Memory Usage', () => {
    it('should not cause memory leaks with repeated operations', async () => {
      const userId = 'test-user'
      const iterations = 50
      
      for (let i = 0; i < iterations; i++) {
        const keywords = createMockKeywords(10)
        await generateReport(keywords, 'daily')
        
        // Create and delete keywords
        const keyword = await MockKeywordService.createKeyword(userId, {
          name: `临时关键词 ${i}`,
          description: '临时描述',
          category: '测试'
        })
        
        await MockKeywordService.deleteKeyword(keyword.id)
      }
      
      // If we reach here without running out of memory, the test passes
      expect(true).toBe(true)
    })
  })

  describe('Data Structure Efficiency', () => {
    it('should handle complex nested data structures efficiently', async () => {
      const keywords = createMockKeywords(20)
      
      const startTime = performance.now()
      
      const report = await generateReport(keywords, 'monthly')
      
      // Verify complex data structures are properly created
      expect(report.keyword_trends).toBeDefined()
      expect(report.insights).toBeDefined()
      expect(report.summary).toBeDefined()
      
      // Check that all trends have proper data points
      report.keyword_trends.forEach(trend => {
        expect(trend.data_points).toBeDefined()
        expect(Array.isArray(trend.data_points)).toBe(true)
        expect(trend.data_points.length).toBeGreaterThan(0)
      })
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      expect(duration).toBeLessThan(8000) // Should complete within 8 seconds
    })
  })
})
