import { MockKeywordService } from '@/lib/api/keywords-mock'
import type { Keyword } from '@/types/keyword'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock as Storage

describe('Keyword Management', () => {
  const mockUserId = 'test-user-id'
  
  beforeEach(() => {
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
    localStorageMock.clear.mockClear()
  })

  describe('MockKeywordService', () => {
    it('should create a keyword successfully', async () => {
      localStorageMock.getItem.mockReturnValue('[]')
      
      const keywordData = {
        name: '人工智能',
        description: 'AI相关趋势',
        category: '科技'
      }

      const result = await MockKeywordService.createKeyword(mockUserId, keywordData)

      expect(result).toMatchObject({
        user_id: mockUserId,
        name: keywordData.name,
        description: keywordData.description,
        category: keywordData.category,
        is_active: true
      })
      expect(result.id).toBeDefined()
      expect(result.created_at).toBeDefined()
      expect(result.updated_at).toBeDefined()
    })

    it('should prevent duplicate keywords', async () => {
      const existingKeyword: Keyword = {
        id: 'existing-id',
        user_id: mockUserId,
        name: '人工智能',
        description: 'Existing AI keyword',
        category: '科技',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify([existingKeyword]))

      const keywordData = {
        name: '人工智能', // Same name as existing
        description: 'New AI keyword',
        category: '科技'
      }

      await expect(MockKeywordService.createKeyword(mockUserId, keywordData))
        .rejects.toThrow('该关键词已存在')
    })

    it('should get keywords for a user', async () => {
      const mockKeywords: Keyword[] = [
        {
          id: 'keyword-1',
          user_id: mockUserId,
          name: '人工智能',
          description: 'AI trends',
          category: '科技',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'keyword-2',
          user_id: 'other-user',
          name: '区块链',
          description: 'Blockchain trends',
          category: '科技',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockKeywords))

      const result = await MockKeywordService.getKeywords(mockUserId)

      expect(result).toHaveLength(1)
      expect(result[0].user_id).toBe(mockUserId)
      expect(result[0].name).toBe('人工智能')
    })

    it('should update a keyword', async () => {
      const existingKeyword: Keyword = {
        id: 'keyword-1',
        user_id: mockUserId,
        name: '人工智能',
        description: 'AI trends',
        category: '科技',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify([existingKeyword]))

      const updates = {
        description: 'Updated AI trends description',
        is_active: false
      }

      const result = await MockKeywordService.updateKeyword('keyword-1', updates)

      expect(result.description).toBe(updates.description)
      expect(result.is_active).toBe(updates.is_active)
      expect(result.name).toBe(existingKeyword.name) // Should remain unchanged
    })

    it('should delete a keyword', async () => {
      const existingKeyword: Keyword = {
        id: 'keyword-1',
        user_id: mockUserId,
        name: '人工智能',
        description: 'AI trends',
        category: '科技',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      localStorageMock.getItem.mockReturnValue(JSON.stringify([existingKeyword]))

      await MockKeywordService.deleteKeyword('keyword-1')

      // Should call setItem with empty array
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'baobao_keywords',
        JSON.stringify([])
      )
    })

    it('should generate keyword statistics', async () => {
      const mockKeywords: Keyword[] = [
        {
          id: 'keyword-1',
          user_id: mockUserId,
          name: '人工智能',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_report_generated_at: new Date().toISOString()
        } as Keyword,
        {
          id: 'keyword-2',
          user_id: mockUserId,
          name: '区块链',
          is_active: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as Keyword
      ]

      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockKeywords))

      const stats = await MockKeywordService.getKeywordStats(mockUserId)

      expect(stats.total_keywords).toBe(2)
      expect(stats.active_keywords).toBe(1)
      expect(stats.reports_generated).toBe(1)
      expect(stats.last_update).toBeDefined()
    })
  })
})
