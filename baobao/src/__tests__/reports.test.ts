import { generateReport } from '@/lib/ai/report-generator'
import type { Keyword } from '@/types/keyword'

describe('Report Generation', () => {
  const mockKeywords: Keyword[] = [
    {
      id: 'keyword-1',
      user_id: 'test-user',
      name: '人工智能',
      description: 'AI trends',
      category: '科技',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'keyword-2',
      user_id: 'test-user',
      name: '区块链',
      description: 'Blockchain trends',
      category: '科技',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]

  describe('generateReport', () => {
    it('should generate a daily report successfully', async () => {
      const report = await generateReport(mockKeywords, 'daily')

      expect(report.type).toBe('daily')
      expect(report.status).toBe('completed')
      expect(report.keywords).toEqual(['keyword-1', 'keyword-2'])
      expect(report.keyword_trends).toHaveLength(2)
      expect(report.summary).toBeDefined()
      expect(report.insights).toBeDefined()
      expect(report.generated_at).toBeDefined()
    })

    it('should generate a weekly report successfully', async () => {
      const report = await generateReport(mockKeywords, 'weekly')

      expect(report.type).toBe('weekly')
      expect(report.status).toBe('completed')
      expect(report.keyword_trends).toHaveLength(2)
    })

    it('should generate a monthly report successfully', async () => {
      const report = await generateReport(mockKeywords, 'monthly')

      expect(report.type).toBe('monthly')
      expect(report.status).toBe('completed')
      expect(report.keyword_trends).toHaveLength(2)
    })

    it('should include keyword trends for all keywords', async () => {
      const report = await generateReport(mockKeywords, 'daily')

      expect(report.keyword_trends).toHaveLength(mockKeywords.length)
      
      report.keyword_trends.forEach((trend, index) => {
        expect(trend.keyword_id).toBe(mockKeywords[index].id)
        expect(trend.keyword_name).toBe(mockKeywords[index].name)
        expect(trend.trend_direction).toMatch(/^(up|down|stable)$/)
        expect(trend.trend_percentage).toBeGreaterThanOrEqual(0)
        expect(trend.current_score).toBeGreaterThanOrEqual(0)
        expect(trend.previous_score).toBeGreaterThanOrEqual(0)
        expect(trend.data_points).toBeDefined()
        expect(Array.isArray(trend.data_points)).toBe(true)
      })
    })

    it('should generate meaningful insights', async () => {
      const report = await generateReport(mockKeywords, 'daily')

      expect(report.insights).toBeDefined()
      expect(Array.isArray(report.insights)).toBe(true)
      expect(report.insights.length).toBeGreaterThan(0)

      report.insights.forEach(insight => {
        expect(insight.type).toMatch(/^(trend|anomaly|opportunity|warning)$/)
        expect(insight.title).toBeDefined()
        expect(insight.description).toBeDefined()
        expect(insight.impact_level).toMatch(/^(low|medium|high)$/)
        expect(Array.isArray(insight.keywords)).toBe(true)
      })
    })

    it('should generate accurate summary statistics', async () => {
      const report = await generateReport(mockKeywords, 'daily')

      expect(report.summary.total_keywords).toBe(mockKeywords.length)
      expect(report.summary.trending_up).toBeGreaterThanOrEqual(0)
      expect(report.summary.trending_down).toBeGreaterThanOrEqual(0)
      expect(report.summary.stable).toBeGreaterThanOrEqual(0)
      expect(
        report.summary.trending_up + 
        report.summary.trending_down + 
        report.summary.stable
      ).toBe(mockKeywords.length)
      expect(report.summary.top_performer).toBeDefined()
      expect(report.summary.biggest_change).toBeDefined()
      expect(report.summary.overall_sentiment).toMatch(/^(positive|negative|neutral)$/)
    })

    it('should handle custom titles', async () => {
      const customTitle = '自定义报告标题'
      const report = await generateReport(mockKeywords, 'daily', customTitle)

      expect(report.title).toBe(customTitle)
    })

    it('should generate appropriate date ranges', async () => {
      const report = await generateReport(mockKeywords, 'weekly')

      expect(report.date_range.start_date).toBeDefined()
      expect(report.date_range.end_date).toBeDefined()
      
      const startDate = new Date(report.date_range.start_date)
      const endDate = new Date(report.date_range.end_date)
      
      expect(startDate).toBeInstanceOf(Date)
      expect(endDate).toBeInstanceOf(Date)
      expect(startDate.getTime()).toBeLessThan(endDate.getTime())
    })

    it('should handle empty keyword list', async () => {
      const report = await generateReport([], 'daily')

      expect(report.keyword_trends).toHaveLength(0)
      expect(report.summary.total_keywords).toBe(0)
      expect(report.insights).toHaveLength(0)
    })

    it('should generate trend data with proper structure', async () => {
      const report = await generateReport(mockKeywords, 'daily')

      report.keyword_trends.forEach(trend => {
        expect(trend.data_points).toBeDefined()
        expect(Array.isArray(trend.data_points)).toBe(true)
        
        trend.data_points.forEach(point => {
          expect(point.date).toBeDefined()
          expect(point.value).toBeGreaterThanOrEqual(0)
          expect(point.value).toBeLessThanOrEqual(100)
          expect(typeof point.change_percentage).toBe('number')
        })
      })
    })
  })
})
