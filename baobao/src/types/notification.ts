export type NotificationFrequency = 'daily' | 'weekly' | 'monthly' | 'custom'

export type WeekDay = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday'

export interface NotificationSettings {
  id: string
  user_id: string
  is_enabled: boolean
  frequency: NotificationFrequency
  time: string // HH:MM format
  timezone: string
  week_days?: WeekDay[] // For weekly frequency
  month_day?: number // For monthly frequency (1-31)
  keywords?: string[] // Specific keywords to include, empty means all active keywords
  email_enabled: boolean
  push_enabled: boolean
  created_at: string
  updated_at: string
}

export interface CreateNotificationSettingsRequest {
  frequency: NotificationFrequency
  time: string
  timezone?: string
  week_days?: WeekDay[]
  month_day?: number
  keywords?: string[]
  email_enabled?: boolean
  push_enabled?: boolean
}

export interface UpdateNotificationSettingsRequest {
  is_enabled?: boolean
  frequency?: NotificationFrequency
  time?: string
  timezone?: string
  week_days?: WeekDay[]
  month_day?: number
  keywords?: string[]
  email_enabled?: boolean
  push_enabled?: boolean
}

export interface NotificationPreview {
  next_send_time: string
  frequency_description: string
  keywords_count: number
  delivery_methods: string[]
}

// 预设的时间选项
export const TIME_PRESETS = [
  { value: '06:00', label: '早上 6:00' },
  { value: '07:00', label: '早上 7:00' },
  { value: '08:00', label: '早上 8:00' },
  { value: '09:00', label: '早上 9:00' },
  { value: '10:00', label: '上午 10:00' },
  { value: '12:00', label: '中午 12:00' },
  { value: '14:00', label: '下午 2:00' },
  { value: '16:00', label: '下午 4:00' },
  { value: '18:00', label: '下午 6:00' },
  { value: '20:00', label: '晚上 8:00' },
  { value: '21:00', label: '晚上 9:00' },
  { value: '22:00', label: '晚上 10:00' },
]

// 星期选项
export const WEEK_DAYS_OPTIONS = [
  { value: 'monday', label: '周一', short: '一' },
  { value: 'tuesday', label: '周二', short: '二' },
  { value: 'wednesday', label: '周三', short: '三' },
  { value: 'thursday', label: '周四', short: '四' },
  { value: 'friday', label: '周五', short: '五' },
  { value: 'saturday', label: '周六', short: '六' },
  { value: 'sunday', label: '周日', short: '日' },
] as const

// 频率选项
export const FREQUENCY_OPTIONS = [
  { 
    value: 'daily', 
    label: '每日推送', 
    description: '每天在指定时间发送报告',
    icon: '📅'
  },
  { 
    value: 'weekly', 
    label: '每周推送', 
    description: '每周在指定日期和时间发送报告',
    icon: '📊'
  },
  { 
    value: 'monthly', 
    label: '每月推送', 
    description: '每月在指定日期和时间发送报告',
    icon: '📈'
  },
] as const
