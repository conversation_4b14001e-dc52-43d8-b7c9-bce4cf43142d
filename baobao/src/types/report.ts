export type ReportStatus = 'pending' | 'generating' | 'completed' | 'failed'

export type ReportType = 'daily' | 'weekly' | 'monthly' | 'custom'

export interface TrendData {
  date: string
  value: number
  change_percentage?: number
}

export interface KeywordTrend {
  keyword_id: string
  keyword_name: string
  trend_direction: 'up' | 'down' | 'stable'
  trend_percentage: number
  current_score: number
  previous_score: number
  data_points: TrendData[]
}

export interface ReportInsight {
  type: 'trend' | 'anomaly' | 'opportunity' | 'warning'
  title: string
  description: string
  impact_level: 'low' | 'medium' | 'high'
  keywords: string[]
  data?: any
}

export interface ReportSummary {
  total_keywords: number
  trending_up: number
  trending_down: number
  stable: number
  top_performer: string
  biggest_change: string
  overall_sentiment: 'positive' | 'negative' | 'neutral'
}

export interface Report {
  id: string
  user_id: string
  title: string
  description?: string
  type: ReportType
  status: ReportStatus
  keywords: string[] // keyword IDs
  date_range: {
    start_date: string
    end_date: string
  }
  summary: ReportSummary
  keyword_trends: KeywordTrend[]
  insights: ReportInsight[]
  generated_at: string
  created_at: string
  updated_at: string
  error_message?: string
}

export interface CreateReportRequest {
  title?: string
  description?: string
  type: ReportType
  keywords?: string[] // If empty, include all active keywords
  date_range?: {
    start_date: string
    end_date: string
  }
}

export interface ReportFilters {
  status?: ReportStatus
  type?: ReportType
  keyword_id?: string
  date_from?: string
  date_to?: string
}

export interface ReportStats {
  total_reports: number
  completed_reports: number
  failed_reports: number
  last_generated: string | null
  average_generation_time: number // in seconds
}

// 预设的报告模板
export const REPORT_TEMPLATES = [
  {
    id: 'daily-summary',
    name: '每日趋势摘要',
    description: '过去24小时的关键词趋势变化',
    type: 'daily' as ReportType,
    icon: '📊'
  },
  {
    id: 'weekly-analysis',
    name: '周度深度分析',
    description: '过去一周的详细趋势分析和洞察',
    type: 'weekly' as ReportType,
    icon: '📈'
  },
  {
    id: 'monthly-overview',
    name: '月度全景报告',
    description: '过去一个月的全面趋势回顾',
    type: 'monthly' as ReportType,
    icon: '📋'
  },
] as const

// 趋势方向的显示配置
export const TREND_CONFIG = {
  up: {
    label: '上升',
    color: 'text-success',
    bgColor: 'bg-success/10',
    icon: '📈'
  },
  down: {
    label: '下降',
    color: 'text-destructive',
    bgColor: 'bg-destructive/10',
    icon: '📉'
  },
  stable: {
    label: '稳定',
    color: 'text-muted-foreground',
    bgColor: 'bg-muted/50',
    icon: '➡️'
  }
} as const

// 洞察类型的显示配置
export const INSIGHT_CONFIG = {
  trend: {
    label: '趋势洞察',
    color: 'text-info',
    bgColor: 'bg-info/10',
    icon: '🔍'
  },
  anomaly: {
    label: '异常检测',
    color: 'text-warning',
    bgColor: 'bg-warning/10',
    icon: '⚠️'
  },
  opportunity: {
    label: '机会发现',
    color: 'text-success',
    bgColor: 'bg-success/10',
    icon: '💡'
  },
  warning: {
    label: '风险提醒',
    color: 'text-destructive',
    bgColor: 'bg-destructive/10',
    icon: '🚨'
  }
} as const
