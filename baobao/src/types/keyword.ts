export interface Keyword {
  id: string
  user_id: string
  name: string
  description?: string
  category?: string
  is_active: boolean
  created_at: string
  updated_at: string
  last_report_generated_at?: string
}

export interface KeywordFormData {
  name: string
  description?: string
  category?: string
}

export interface KeywordStats {
  total_keywords: number
  active_keywords: number
  reports_generated: number
  last_update: string
}

export interface CreateKeywordRequest {
  name: string
  description?: string
  category?: string
}

export interface UpdateKeywordRequest {
  name?: string
  description?: string
  category?: string
  is_active?: boolean
}
