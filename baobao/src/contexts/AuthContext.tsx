'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import type { AuthContextType, User, LoginCredentials, RegisterCredentials } from '@/types/auth'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 模拟用户存储
const STORAGE_KEY = 'baobao_user'

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 检查本地存储中的用户信息
    const checkStoredUser = () => {
      try {
        const storedUser = localStorage.getItem(STORAGE_KEY)
        if (storedUser) {
          setUser(JSON.parse(storedUser))
        }
      } catch (error) {
        console.error('读取用户信息失败:', error)
      } finally {
        setLoading(false)
      }
    }

    checkStoredUser()
  }, [])

  const signIn = async (credentials: LoginCredentials) => {
    setLoading(true)
    setError(null)

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 简单的模拟验证
      if (credentials.email === '<EMAIL>' && credentials.password === 'demo123456') {
        const mockUser: User = {
          id: 'demo-user-id',
          email: credentials.email,
          name: '演示用户',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }

        setUser(mockUser)
        localStorage.setItem(STORAGE_KEY, JSON.stringify(mockUser))
      } else {
        throw new Error('邮箱或密码错误')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (credentials: RegisterCredentials) => {
    setLoading(true)
    setError(null)

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 简单的模拟注册
      if (credentials.email && credentials.password) {
        // 在真实应用中，这里会创建用户账户
        // 现在我们只是模拟成功
        console.log('模拟注册成功:', credentials.email)
      } else {
        throw new Error('请填写完整信息')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '注册失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    setError(null)

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      setUser(null)
      localStorage.removeItem(STORAGE_KEY)
    } catch (err) {
      setError(err instanceof Error ? err.message : '登出失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    setLoading(true)
    setError(null)

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟发送重置邮件
      console.log('模拟发送重置密码邮件到:', email)
    } catch (err) {
      setError(err instanceof Error ? err.message : '重置密码失败')
      throw err
    } finally {
      setLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
