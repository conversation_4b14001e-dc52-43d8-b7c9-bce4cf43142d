'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Clock, 
  Calendar, 
  Mail, 
  Bell, 
  Target,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { NotificationPreview, NotificationSettings } from '@/types/notification'

interface NotificationPreviewProps {
  settings: NotificationSettings
  preview: NotificationPreview
  loading?: boolean
}

export function NotificationPreview({ settings, preview, loading = false }: NotificationPreviewProps) {
  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-muted rounded w-1/3"></div>
            <div className="h-3 bg-muted rounded w-2/3"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const nextSendDate = new Date(preview.next_send_time)
  const isToday = nextSendDate.toDateString() === new Date().toDateString()
  const isTomorrow = nextSendDate.toDateString() === new Date(Date.now() + 24 * 60 * 60 * 1000).toDateString()

  const getDateLabel = () => {
    if (isToday) return '今天'
    if (isTomorrow) return '明天'
    return format(nextSendDate, 'MM月dd日', { locale: zhCN })
  }

  const getTimeLabel = () => {
    return format(nextSendDate, 'HH:mm')
  }

  return (
    <Card className={settings.is_enabled ? 'border-success/20 bg-success/5' : 'border-warning/20 bg-warning/5'}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {settings.is_enabled ? (
            <CheckCircle className="h-5 w-5 text-success" />
          ) : (
            <AlertCircle className="h-5 w-5 text-warning" />
          )}
          推送预览
        </CardTitle>
        <CardDescription>
          {settings.is_enabled ? '下次推送信息预览' : '推送已停用，以下是配置预览'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 下次推送时间 */}
        <div className="flex items-center justify-between p-4 bg-background rounded-lg border">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-brand-primary/10 rounded-full">
              <Clock className="h-4 w-4 text-brand-primary" />
            </div>
            <div>
              <p className="font-medium">下次推送时间</p>
              <p className="text-sm text-muted-foreground">
                {getDateLabel()} {getTimeLabel()}
              </p>
            </div>
          </div>
          <Badge variant={settings.is_enabled ? "success" : "secondary"}>
            {settings.is_enabled ? '已启用' : '已停用'}
          </Badge>
        </div>

        {/* 推送频率 */}
        <div className="flex items-center space-x-3">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <div>
            <span className="text-sm font-medium">推送频率：</span>
            <span className="text-sm text-muted-foreground ml-1">
              {preview.frequency_description}
            </span>
          </div>
        </div>

        {/* 包含的关键词 */}
        <div className="flex items-center space-x-3">
          <Target className="h-4 w-4 text-muted-foreground" />
          <div>
            <span className="text-sm font-medium">包含关键词：</span>
            <span className="text-sm text-muted-foreground ml-1">
              {preview.keywords_count > 0 
                ? `${preview.keywords_count} 个指定关键词`
                : '所有活跃关键词'
              }
            </span>
          </div>
        </div>

        {/* 推送方式 */}
        <div className="space-y-2">
          <p className="text-sm font-medium">推送方式：</p>
          <div className="flex flex-wrap gap-2">
            {preview.delivery_methods.map((method, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {method === '邮件' ? (
                  <Mail className="h-3 w-3" />
                ) : (
                  <Bell className="h-3 w-3" />
                )}
                {method}
              </Badge>
            ))}
            {preview.delivery_methods.length === 0 && (
              <Badge variant="secondary">未选择推送方式</Badge>
            )}
          </div>
        </div>

        {/* 状态提示 */}
        {!settings.is_enabled && (
          <div className="p-3 bg-warning/10 border border-warning/20 rounded-lg">
            <p className="text-sm text-warning-foreground">
              ⚠️ 推送功能已停用，启用后将按照上述设置发送报告
            </p>
          </div>
        )}

        {settings.is_enabled && preview.delivery_methods.length === 0 && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive-foreground">
              ⚠️ 未选择任何推送方式，请至少启用一种推送方式
            </p>
          </div>
        )}

        {settings.is_enabled && isToday && (
          <div className="p-3 bg-info/10 border border-info/20 rounded-lg">
            <p className="text-sm text-info-foreground">
              📅 今天将会发送报告，请确保您的邮箱设置正确
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
