'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Clock, 
  Calendar, 
  Mail, 
  Bell, 
  Save, 
  Trash2,
  Power,
  PowerOff,
  Settings as SettingsIcon
} from 'lucide-react'
import { 
  FREQUENCY_OPTIONS, 
  TIME_PRESETS, 
  WEEK_DAYS_OPTIONS,
  type NotificationFrequency,
  type WeekDay,
  type CreateNotificationSettingsRequest,
  type UpdateNotificationSettingsRequest
} from '@/types/notification'
import type { Keyword } from '@/types/keyword'

interface NotificationSettingsFormProps {
  keywords: Keyword[]
  initialSettings?: any
  onSave: (settings: CreateNotificationSettingsRequest | UpdateNotificationSettingsRequest) => Promise<void>
  onDelete?: () => Promise<void>
  onToggle?: (enabled: boolean) => Promise<void>
  loading?: boolean
  isEditing?: boolean
}

export function NotificationSettingsForm({
  keywords,
  initialSettings,
  onSave,
  onDelete,
  onToggle,
  loading = false,
  isEditing = false
}: NotificationSettingsFormProps) {
  const [frequency, setFrequency] = useState<NotificationFrequency>('daily')
  const [time, setTime] = useState('09:00')
  const [weekDays, setWeekDays] = useState<WeekDay[]>(['monday'])
  const [monthDay, setMonthDay] = useState(1)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [emailEnabled, setEmailEnabled] = useState(true)
  const [pushEnabled, setPushEnabled] = useState(false)
  const [customTime, setCustomTime] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 初始化表单数据
  useEffect(() => {
    if (initialSettings) {
      setFrequency(initialSettings.frequency || 'daily')
      setTime(initialSettings.time || '09:00')
      setWeekDays(initialSettings.week_days || ['monday'])
      setMonthDay(initialSettings.month_day || 1)
      setSelectedKeywords(initialSettings.keywords || [])
      setEmailEnabled(initialSettings.email_enabled ?? true)
      setPushEnabled(initialSettings.push_enabled ?? false)
    }
  }, [initialSettings])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 验证表单
    const newErrors: Record<string, string> = {}
    
    if (!time) {
      newErrors.time = '请选择推送时间'
    }
    
    if (frequency === 'weekly' && weekDays.length === 0) {
      newErrors.weekDays = '请选择至少一个星期几'
    }
    
    if (frequency === 'monthly' && (monthDay < 1 || monthDay > 31)) {
      newErrors.monthDay = '请选择有效的日期（1-31）'
    }

    setErrors(newErrors)
    
    if (Object.keys(newErrors).length > 0) {
      return
    }

    const finalTime = customTime || time

    const settingsData = {
      frequency,
      time: finalTime,
      week_days: frequency === 'weekly' ? weekDays : undefined,
      month_day: frequency === 'monthly' ? monthDay : undefined,
      keywords: selectedKeywords.length > 0 ? selectedKeywords : undefined,
      email_enabled: emailEnabled,
      push_enabled: pushEnabled,
    }

    try {
      await onSave(settingsData)
      setErrors({})
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  const toggleWeekDay = (day: WeekDay) => {
    setWeekDays(prev => 
      prev.includes(day) 
        ? prev.filter(d => d !== day)
        : [...prev, day]
    )
  }

  const toggleKeyword = (keywordId: string) => {
    setSelectedKeywords(prev => 
      prev.includes(keywordId)
        ? prev.filter(id => id !== keywordId)
        : [...prev, keywordId]
    )
  }

  const activeKeywords = keywords.filter(k => k.is_active)

  return (
    <div className="space-y-6">
      {/* 设置状态和操作 */}
      {isEditing && initialSettings && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${initialSettings.is_enabled ? 'bg-success/10' : 'bg-muted'}`}>
                  {initialSettings.is_enabled ? (
                    <Power className="h-5 w-5 text-success" />
                  ) : (
                    <PowerOff className="h-5 w-5 text-muted-foreground" />
                  )}
                </div>
                <div>
                  <h3 className="font-semibold">
                    推送设置 {initialSettings.is_enabled ? '已启用' : '已停用'}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {initialSettings.is_enabled ? '系统将按照设置的时间发送报告' : '推送功能已暂停'}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant={initialSettings.is_enabled ? "outline" : "default"}
                  onClick={() => onToggle?.(!initialSettings.is_enabled)}
                  disabled={loading}
                >
                  {initialSettings.is_enabled ? '停用' : '启用'}
                </Button>
                {onDelete && (
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={onDelete}
                    disabled={loading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 设置表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="h-5 w-5" />
            {isEditing ? '编辑推送设置' : '创建推送设置'}
          </CardTitle>
          <CardDescription>
            配置报告的推送频率、时间和内容
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 推送频率 */}
            <div className="space-y-3">
              <label className="text-sm font-medium">推送频率</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {FREQUENCY_OPTIONS.map((option) => (
                  <div
                    key={option.value}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      frequency === option.value
                        ? 'border-brand-primary bg-brand-primary/5'
                        : 'border-border hover:border-brand-primary/50'
                    }`}
                    onClick={() => setFrequency(option.value)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{option.icon}</span>
                      <div>
                        <h4 className="font-medium">{option.label}</h4>
                        <p className="text-xs text-muted-foreground">{option.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 推送时间 */}
            <div className="space-y-3">
              <label className="text-sm font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                推送时间
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2">
                {TIME_PRESETS.map((preset) => (
                  <Button
                    key={preset.value}
                    type="button"
                    variant={time === preset.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setTime(preset.value)
                      setCustomTime('')
                    }}
                    className="text-xs"
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">或自定义时间：</span>
                <Input
                  type="time"
                  value={customTime}
                  onChange={(e) => {
                    setCustomTime(e.target.value)
                    setTime('')
                  }}
                  className="w-32"
                />
              </div>
              {errors.time && (
                <p className="text-sm text-destructive">{errors.time}</p>
              )}
            </div>

            {/* 星期选择（仅周推送） */}
            {frequency === 'weekly' && (
              <div className="space-y-3">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  选择星期几
                </label>
                <div className="flex flex-wrap gap-2">
                  {WEEK_DAYS_OPTIONS.map((day) => (
                    <Button
                      key={day.value}
                      type="button"
                      variant={weekDays.includes(day.value) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleWeekDay(day.value)}
                      className="w-12 h-12 p-0"
                    >
                      {day.short}
                    </Button>
                  ))}
                </div>
                {errors.weekDays && (
                  <p className="text-sm text-destructive">{errors.weekDays}</p>
                )}
              </div>
            )}

            {/* 日期选择（仅月推送） */}
            {frequency === 'monthly' && (
              <div className="space-y-3">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  选择日期
                </label>
                <div className="flex items-center space-x-2">
                  <span className="text-sm">每月</span>
                  <Input
                    type="number"
                    min="1"
                    max="31"
                    value={monthDay}
                    onChange={(e) => setMonthDay(parseInt(e.target.value) || 1)}
                    className="w-20"
                  />
                  <span className="text-sm">日</span>
                </div>
                {errors.monthDay && (
                  <p className="text-sm text-destructive">{errors.monthDay}</p>
                )}
              </div>
            )}

            {/* 关键词选择 */}
            {activeKeywords.length > 0 && (
              <div className="space-y-3">
                <label className="text-sm font-medium">包含的关键词</label>
                <p className="text-xs text-muted-foreground">
                  不选择任何关键词将包含所有活跃的关键词
                </p>
                <div className="flex flex-wrap gap-2">
                  {activeKeywords.map((keyword) => (
                    <Badge
                      key={keyword.id}
                      variant={selectedKeywords.includes(keyword.id) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => toggleKeyword(keyword.id)}
                    >
                      {keyword.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* 推送方式 */}
            <div className="space-y-3">
              <label className="text-sm font-medium">推送方式</label>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="email"
                    checked={emailEnabled}
                    onChange={(e) => setEmailEnabled(e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="email" className="flex items-center space-x-2 text-sm">
                    <Mail className="h-4 w-4" />
                    <span>邮件推送</span>
                  </label>
                </div>
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="push"
                    checked={pushEnabled}
                    onChange={(e) => setPushEnabled(e.target.checked)}
                    className="rounded"
                  />
                  <label htmlFor="push" className="flex items-center space-x-2 text-sm">
                    <Bell className="h-4 w-4" />
                    <span>浏览器推送</span>
                  </label>
                </div>
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="submit" variant="brand" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? '保存中...' : '保存设置'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
