'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts'

interface PieChartProps {
  data: Array<{
    name: string
    value: number
    color?: string
  }>
  height?: number
  showLegend?: boolean
}

const DEFAULT_COLORS = [
  '#6366f1', // brand-primary
  '#8b5cf6', // brand-secondary  
  '#06b6d4', // brand-accent
  '#10b981', // success
  '#f59e0b', // warning
  '#ef4444', // destructive
  '#64748b', // muted
]

export function CustomPieChart({ 
  data, 
  height = 200, 
  showLegend = true 
}: PieChartProps) {
  if (!data || data.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-muted/20 rounded-lg"
        style={{ height }}
      >
        <p className="text-muted-foreground text-sm">暂无数据</p>
      </div>
    )
  }

  // 为数据添加颜色
  const dataWithColors = data.map((item, index) => ({
    ...item,
    color: item.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length]
  }))

  const renderCustomTooltip = ({ active, payload }: { active?: boolean; payload?: Array<{ payload: { name: string; value: number } }> }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-border rounded-lg shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            数量: {data.value}
          </p>
          <p className="text-sm text-muted-foreground">
            占比: {((data.value / dataWithColors.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%
          </p>
        </div>
      )
    }
    return null
  }

  const renderCustomLegend = (props: { payload?: Array<{ value: string; color: string }> }) => {
    const { payload } = props
    return (
      <div className="flex flex-wrap justify-center gap-2 mt-2">
        {payload?.map((entry, index: number) => (
          <div key={index} className="flex items-center gap-1 text-xs">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-muted-foreground">{entry.value}</span>
          </div>
        ))}
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={dataWithColors}
          cx="50%"
          cy="50%"
          innerRadius={showLegend ? 40 : 50}
          outerRadius={showLegend ? 70 : 80}
          paddingAngle={2}
          dataKey="value"
        >
          {dataWithColors.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={renderCustomTooltip} />
        {showLegend && <Legend content={renderCustomLegend} />}
      </PieChart>
    </ResponsiveContainer>
  )
}
