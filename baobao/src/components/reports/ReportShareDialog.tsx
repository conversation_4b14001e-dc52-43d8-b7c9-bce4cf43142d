'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  Share2, 
  Copy, 
  Mail, 
  MessageCircle,
  Download,
  QrCode,
  Link,
  Check
} from 'lucide-react'
import type { Report } from '@/types/report'

interface ReportShareDialogProps {
  isOpen: boolean
  onClose: () => void
  report: Report
}

export function ReportShareDialog({ isOpen, onClose, report }: ReportShareDialogProps) {
  const [copied, setCopied] = useState(false)
  const [shareUrl] = useState(`${window.location.origin}/reports/shared/${report.id}`)

  if (!isOpen) return null

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const handleEmailShare = () => {
    const subject = encodeURIComponent(`分享报告：${report.title}`)
    const body = encodeURIComponent(`我想与您分享这份趋势分析报告：\n\n${report.title}\n\n查看链接：${shareUrl}`)
    window.open(`mailto:?subject=${subject}&body=${body}`)
  }

  const handleSocialShare = (platform: string) => {
    const text = encodeURIComponent(`查看这份有趣的趋势分析报告：${report.title}`)
    const url = encodeURIComponent(shareUrl)
    
    switch (platform) {
      case 'wechat':
        // 微信分享通常需要特殊处理
        alert('请复制链接后在微信中分享')
        handleCopyLink()
        break
      case 'weibo':
        window.open(`https://service.weibo.com/share/share.php?url=${url}&title=${text}`)
        break
      case 'qq':
        window.open(`https://connect.qq.com/widget/shareqq/index.html?url=${url}&title=${text}`)
        break
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Share2 className="h-5 w-5 text-brand-primary" />
                分享报告
              </CardTitle>
              <CardDescription>
                与他人分享您的趋势分析报告
              </CardDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Report Preview */}
          <div className="p-4 bg-muted/30 rounded-lg">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-brand-primary/10 rounded-lg">
                <Share2 className="h-5 w-5 text-brand-primary" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-foreground mb-1">{report.title}</h3>
                <p className="text-sm text-muted-foreground mb-2">
                  {report.description || '基于关键词的趋势分析报告'}
                </p>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" size="sm">
                    {report.keywords.length} 个关键词
                  </Badge>
                  <Badge variant="outline" size="sm">
                    {report.insights.length} 个洞察
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Share Link */}
          <div className="space-y-3">
            <label className="text-sm font-medium">分享链接</label>
            <div className="flex gap-2">
              <Input
                value={shareUrl}
                readOnly
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleCopyLink}
                className="flex-shrink-0"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    已复制
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4 mr-2" />
                    复制
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Share Options */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">分享方式</h4>
            
            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-auto p-4 justify-start"
                onClick={handleEmailShare}
              >
                <Mail className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <p className="font-medium">邮件分享</p>
                  <p className="text-xs text-muted-foreground">通过邮件发送</p>
                </div>
              </Button>
              
              <Button
                variant="outline"
                className="h-auto p-4 justify-start"
                onClick={() => handleSocialShare('wechat')}
              >
                <MessageCircle className="h-5 w-5 mr-3" />
                <div className="text-left">
                  <p className="font-medium">微信分享</p>
                  <p className="text-xs text-muted-foreground">分享到微信</p>
                </div>
              </Button>
            </div>

            {/* Social Platforms */}
            <div className="grid grid-cols-3 gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSocialShare('weibo')}
                className="flex-col h-auto p-3"
              >
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mb-1">
                  <span className="text-orange-600 text-sm font-bold">微</span>
                </div>
                <span className="text-xs">微博</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSocialShare('qq')}
                className="flex-col h-auto p-3"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mb-1">
                  <span className="text-blue-600 text-sm font-bold">Q</span>
                </div>
                <span className="text-xs">QQ空间</span>
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyLink}
                className="flex-col h-auto p-3"
              >
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                  <Link className="h-4 w-4 text-gray-600" />
                </div>
                <span className="text-xs">复制链接</span>
              </Button>
            </div>
          </div>

          {/* Advanced Options */}
          <div className="space-y-3 pt-4 border-t">
            <h4 className="text-sm font-medium">高级选项</h4>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" size="sm">
                <QrCode className="h-4 w-4 mr-2" />
                生成二维码
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                下载图片
              </Button>
            </div>
          </div>

          {/* Privacy Notice */}
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              🔒 分享的报告将生成一个安全的访问链接，您可以随时撤销分享权限。
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
