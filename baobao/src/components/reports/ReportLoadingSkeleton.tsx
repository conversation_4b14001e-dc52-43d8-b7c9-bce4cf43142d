'use client'

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { BarChart3, <PERSON>rkles } from 'lucide-react'

export function ReportLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header Skeleton */}
      <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-muted rounded-lg animate-pulse"></div>
              <div className="space-y-2">
                <div className="h-8 w-64 bg-muted rounded animate-pulse"></div>
                <div className="h-4 w-96 bg-muted rounded animate-pulse"></div>
              </div>
            </div>
            <div className="flex space-x-2">
              <div className="h-9 w-20 bg-muted rounded animate-pulse"></div>
              <div className="h-9 w-24 bg-muted rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content Skeleton */}
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Loading Message */}
        <Card className="mb-8 border-brand-primary/20 bg-gradient-to-r from-brand-primary/5 to-brand-secondary/5">
          <CardContent className="p-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="relative">
                <BarChart3 className="h-8 w-8 text-brand-primary animate-pulse" />
                <div className="absolute inset-0 rounded-full border-2 border-brand-primary/20 animate-spin"></div>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-brand-primary mb-1">
                  AI 正在分析您的数据
                </h3>
                <p className="text-muted-foreground text-sm">
                  正在生成深度洞察和趋势分析...
                </p>
              </div>
              <Sparkles className="h-6 w-6 text-yellow-500 animate-bounce" />
            </div>
          </CardContent>
        </Card>

        {/* Summary Cards Skeleton */}
        <div className="mb-12">
          <div className="h-8 w-48 bg-muted rounded animate-pulse mb-6"></div>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <div className="h-4 w-20 bg-muted rounded"></div>
                      <div className="h-8 w-12 bg-muted rounded"></div>
                    </div>
                    <div className="w-12 h-12 bg-muted rounded-full"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Trend Analysis Skeleton */}
        <div className="mb-12">
          <div className="h-8 w-56 bg-muted rounded animate-pulse mb-6"></div>
          <div className="space-y-8">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-6 h-6 bg-muted rounded"></div>
                      <div className="h-6 w-32 bg-muted rounded"></div>
                    </div>
                    <div className="h-6 w-20 bg-muted rounded"></div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-48 bg-muted rounded mb-4"></div>
                  <div className="grid grid-cols-4 gap-4">
                    {[...Array(4)].map((_, j) => (
                      <div key={j} className="h-16 bg-muted rounded"></div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Insights Skeleton */}
        <div className="mb-12">
          <div className="h-8 w-40 bg-muted rounded animate-pulse mb-6"></div>
          <div className="space-y-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-muted rounded-full"></div>
                    <div className="flex-1 space-y-3">
                      <div className="h-6 w-48 bg-muted rounded"></div>
                      <div className="h-4 w-full bg-muted rounded"></div>
                      <div className="h-4 w-3/4 bg-muted rounded"></div>
                      <div className="flex space-x-2">
                        <div className="h-6 w-16 bg-muted rounded"></div>
                        <div className="h-6 w-20 bg-muted rounded"></div>
                        <div className="h-6 w-18 bg-muted rounded"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Progress Indicators */}
        <div className="fixed bottom-8 right-8">
          <Card className="shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-brand-primary/60 rounded-full animate-pulse delay-100"></div>
                <div className="w-2 h-2 bg-brand-primary/30 rounded-full animate-pulse delay-200"></div>
                <span className="text-sm text-muted-foreground ml-2">分析中</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
