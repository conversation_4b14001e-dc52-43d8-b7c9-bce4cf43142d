'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff, 
  Search,
  Calendar,
  Tag,
  FileText
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { Keyword } from '@/types/keyword'

interface KeywordListProps {
  keywords: Keyword[]
  loading?: boolean
  onEdit?: (keyword: Keyword) => void
  onDelete?: (keywordId: string) => void
  onToggleStatus?: (keywordId: string, isActive: boolean) => void
}

export function KeywordList({ 
  keywords, 
  loading = false, 
  onEdit, 
  onDelete, 
  onToggleStatus 
}: KeywordListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [showInactive, setShowInactive] = useState(false)

  // 获取所有分类
  const categories = Array.from(new Set(keywords.map(k => k.category).filter(Boolean)))

  // 过滤关键词
  const filteredKeywords = keywords.filter(keyword => {
    const matchesSearch = keyword.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         keyword.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || keyword.category === selectedCategory
    const matchesStatus = showInactive || keyword.is_active
    
    return matchesSearch && matchesCategory && matchesStatus
  })

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded w-1/3"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-20"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (keywords.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">还没有关键词</h3>
          <p className="text-muted-foreground mb-4">
            添加您的第一个关键词，开始生成趋势分析报告
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索关键词..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background text-sm"
          >
            <option value="">所有分类</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <Button
            variant={showInactive ? "default" : "outline"}
            size="sm"
            onClick={() => setShowInactive(!showInactive)}
          >
            {showInactive ? "显示全部" : "包含已停用"}
          </Button>
        </div>
      </div>

      {/* 关键词列表 */}
      <div className="space-y-4">
        {filteredKeywords.map((keyword) => (
          <Card key={keyword.id} className={!keyword.is_active ? 'opacity-60' : ''}>
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex-1 space-y-3">
                  {/* 标题和状态 */}
                  <div className="flex items-center gap-3">
                    <h3 className="font-semibold text-lg">{keyword.name}</h3>
                    <Badge variant={keyword.is_active ? "success" : "secondary"}>
                      {keyword.is_active ? "活跃" : "已停用"}
                    </Badge>
                    {keyword.category && (
                      <Badge variant="outline">{keyword.category}</Badge>
                    )}
                  </div>

                  {/* 描述 */}
                  {keyword.description && (
                    <p className="text-muted-foreground text-sm">
                      {keyword.description}
                    </p>
                  )}

                  {/* 元信息 */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      创建于 {format(new Date(keyword.created_at), 'yyyy年MM月dd日', { locale: zhCN })}
                    </div>
                    {keyword.last_report_generated_at && (
                      <div className="flex items-center gap-1">
                        <FileText className="h-3 w-3" />
                        最后报告 {format(new Date(keyword.last_report_generated_at), 'MM月dd日', { locale: zhCN })}
                      </div>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center gap-1 sm:gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onToggleStatus?.(keyword.id, !keyword.is_active)}
                    title={keyword.is_active ? "停用关键词" : "启用关键词"}
                  >
                    {keyword.is_active ? (
                      <PowerOff className="h-4 w-4" />
                    ) : (
                      <Power className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEdit?.(keyword)}
                    title="编辑关键词"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDelete?.(keyword.id)}
                    title="删除关键词"
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredKeywords.length === 0 && keywords.length > 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">没有找到匹配的关键词</h3>
            <p className="text-muted-foreground">
              尝试调整搜索条件或过滤器
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
