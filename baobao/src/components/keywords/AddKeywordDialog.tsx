'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, X, Tag, FileText, Folder } from 'lucide-react'
import type { CreateKeywordRequest } from '@/types/keyword'

interface AddKeywordDialogProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: CreateKeywordRequest) => Promise<void>
  loading?: boolean
}

const SUGGESTED_CATEGORIES = [
  '科技', '商业', '健康', '教育', '娱乐', '体育', '政治', '环境', '金融', '社会'
]

const KEYWORD_EXAMPLES = [
  '人工智能大模型',
  '新能源汽车',
  '区块链技术',
  '元宇宙',
  '量子计算',
  '生物技术',
]

export function AddKeywordDialog({ isOpen, onClose, onSubmit, loading = false }: AddKeywordDialogProps) {
  const [formData, setFormData] = useState<CreateKeywordRequest>({
    name: '',
    description: '',
    category: '',
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 验证表单
    const newErrors: Record<string, string> = {}
    if (!formData.name.trim()) {
      newErrors.name = '关键词名称不能为空'
    }
    if (formData.name.length > 50) {
      newErrors.name = '关键词名称不能超过50个字符'
    }
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述不能超过200个字符'
    }

    setErrors(newErrors)
    
    if (Object.keys(newErrors).length > 0) {
      return
    }

    try {
      await onSubmit(formData)
      // 重置表单
      setFormData({ name: '', description: '', category: '' })
      setErrors({})
      onClose()
    } catch (error) {
      console.error('提交失败:', error)
    }
  }

  const handleClose = () => {
    setFormData({ name: '', description: '', category: '' })
    setErrors({})
    onClose()
  }

  const selectExample = (example: string) => {
    setFormData(prev => ({ ...prev, name: example }))
  }

  const selectCategory = (category: string) => {
    setFormData(prev => ({ ...prev, category }))
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5 text-brand-primary" />
                添加关键词
              </CardTitle>
              <CardDescription>
                添加您想要跟踪的关键词，系统将为您生成相关的趋势分析报告
              </CardDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 关键词名称 */}
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                <Tag className="h-4 w-4" />
                关键词名称 *
              </label>
              <Input
                id="name"
                placeholder="例如：人工智能大模型"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            {/* 示例关键词 */}
            <div className="space-y-2">
              <label className="text-sm font-medium">快速选择示例</label>
              <div className="flex flex-wrap gap-2">
                {KEYWORD_EXAMPLES.map((example) => (
                  <Button
                    key={example}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => selectExample(example)}
                    className="text-xs"
                  >
                    {example}
                  </Button>
                ))}
              </div>
            </div>

            {/* 描述 */}
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium flex items-center gap-2">
                <FileText className="h-4 w-4" />
                描述 (可选)
              </label>
              <textarea
                id="description"
                placeholder="简要描述这个关键词的含义或您关注的方面..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${errors.description ? 'border-destructive' : ''}`}
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>

            {/* 分类 */}
            <div className="space-y-2">
              <label htmlFor="category" className="text-sm font-medium flex items-center gap-2">
                <Folder className="h-4 w-4" />
                分类 (可选)
              </label>
              <Input
                id="category"
                placeholder="例如：科技、商业、健康等"
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              />
              <div className="flex flex-wrap gap-2 mt-2">
                {SUGGESTED_CATEGORIES.map((category) => (
                  <Badge
                    key={category}
                    variant="outline"
                    className="cursor-pointer hover:bg-brand-primary hover:text-white transition-colors"
                    onClick={() => selectCategory(category)}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={handleClose}>
                取消
              </Button>
              <Button type="submit" variant="brand" disabled={loading}>
                {loading ? '添加中...' : '添加关键词'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
