'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Trend<PERSON>hart } from '@/components/charts/TrendChart'
import { CustomPieChart } from '@/components/charts/PieChart'
import { TrendingUp, TrendingDown, Target, BarChart3, Calendar } from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { Keyword } from '@/types/keyword'
import type { Report } from '@/types/report'

interface DashboardOverviewProps {
  keywords: Keyword[]
  reports: Report[]
  loading?: boolean
}

export function DashboardOverview({ keywords, reports, loading = false }: DashboardOverviewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded w-1/3"></div>
                <div className="h-32 bg-muted rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // 生成模拟趋势数据
  const generateMockTrendData = () => {
    const data = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      data.push({
        date: date.toISOString().split('T')[0],
        value: 50 + Math.random() * 30 + Math.sin(i * 0.5) * 10
      })
    }
    return data
  }

  // 关键词分类统计
  const keywordsByCategory = keywords.reduce((acc, keyword) => {
    const category = keyword.category || '未分类'
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const categoryData = Object.entries(keywordsByCategory).map(([name, value]) => ({
    name,
    value
  }))

  // 关键词状态统计
  const activeKeywords = keywords.filter(k => k.is_active).length
  const inactiveKeywords = keywords.length - activeKeywords

  const statusData = [
    { name: '活跃', value: activeKeywords, color: '#10b981' },
    { name: '停用', value: inactiveKeywords, color: '#64748b' }
  ].filter(item => item.value > 0)

  // 报告状态统计
  const completedReports = reports.filter(r => r.status === 'completed').length
  const pendingReports = reports.filter(r => r.status === 'generating').length
  const failedReports = reports.filter(r => r.status === 'failed').length

  const reportStatusData = [
    { name: '已完成', value: completedReports, color: '#10b981' },
    { name: '生成中', value: pendingReports, color: '#f59e0b' },
    { name: '失败', value: failedReports, color: '#ef4444' }
  ].filter(item => item.value > 0)

  // 最近活动
  const recentActivity = [
    ...keywords.slice(0, 3).map(k => ({
      type: 'keyword',
      title: `添加关键词：${k.name}`,
      time: k.created_at,
      icon: Target
    })),
    ...reports.slice(0, 2).map(r => ({
      type: 'report',
      title: `生成报告：${r.title}`,
      time: r.created_at,
      icon: BarChart3
    }))
  ].sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()).slice(0, 5)

  return (
    <div className="space-y-6">
      {/* 趋势图表 */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-success" />
              整体趋势
            </CardTitle>
            <CardDescription>
              过去7天的综合趋势变化
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TrendChart
              data={generateMockTrendData()}
              height={180}
              color="#10b981"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-brand-primary" />
              关键词分布
            </CardTitle>
            <CardDescription>
              按分类统计的关键词分布
            </CardDescription>
          </CardHeader>
          <CardContent>
            {categoryData.length > 0 ? (
              <CustomPieChart
                data={categoryData}
                height={180}
                showLegend={true}
              />
            ) : (
              <div className="flex items-center justify-center h-[200px] bg-muted/20 rounded-lg">
                <p className="text-muted-foreground text-sm">暂无关键词数据</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 状态统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>关键词状态</CardTitle>
            <CardDescription>
              活跃和停用关键词的分布
            </CardDescription>
          </CardHeader>
          <CardContent>
            {statusData.length > 0 ? (
              <CustomPieChart 
                data={statusData} 
                height={180}
                showLegend={true}
              />
            ) : (
              <div className="flex items-center justify-center h-[180px] bg-muted/20 rounded-lg">
                <p className="text-muted-foreground text-sm">暂无数据</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>报告状态</CardTitle>
            <CardDescription>
              报告生成状态分布
            </CardDescription>
          </CardHeader>
          <CardContent>
            {reportStatusData.length > 0 ? (
              <CustomPieChart 
                data={reportStatusData} 
                height={180}
                showLegend={true}
              />
            ) : (
              <div className="flex items-center justify-center h-[180px] bg-muted/20 rounded-lg">
                <p className="text-muted-foreground text-sm">暂无报告数据</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            最近活动
          </CardTitle>
          <CardDescription>
            最新的关键词和报告活动
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentActivity.length > 0 ? (
            <div className="space-y-3">
              {recentActivity.map((activity, index) => {
                const Icon = activity.icon
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-muted/30 rounded-lg">
                    <div className="p-2 bg-brand-primary/10 rounded-full">
                      <Icon className="h-4 w-4 text-brand-primary" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(activity.time), 'MM月dd日 HH:mm', { locale: zhCN })}
                      </p>
                    </div>
                    <Badge variant="outline" size="sm">
                      {activity.type === 'keyword' ? '关键词' : '报告'}
                    </Badge>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">暂无活动记录</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
