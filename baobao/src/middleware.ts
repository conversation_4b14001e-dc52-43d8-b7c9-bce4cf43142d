import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // 在模拟模式下，我们暂时禁用中间件认证检查
  // 在生产环境中，这里会有完整的 Supabase 认证逻辑

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
