import type { 
  Report, 
  KeywordTrend, 
  ReportInsight, 
  ReportSummary, 
  TrendData,
  ReportType 
} from '@/types/report'
import type { Keyword } from '@/types/keyword'

// 模拟AI生成趋势数据
function generateMockTrendData(days: number): TrendData[] {
  const data: TrendData[] = []
  const baseValue = 50 + Math.random() * 30 // 50-80 基础值
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    // 生成带有趋势的随机数据
    const trend = Math.sin(i * 0.3) * 10 // 周期性趋势
    const noise = (Math.random() - 0.5) * 15 // 随机噪声
    const value = Math.max(0, Math.min(100, baseValue + trend + noise))
    
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.round(value * 100) / 100,
      change_percentage: i === days - 1 ? 0 : Math.round((value - data[data.length - 1]?.value || value) / (data[data.length - 1]?.value || value) * 100 * 100) / 100
    })
  }
  
  return data
}

// 生成关键词趋势分析
function generateKeywordTrend(keyword: Keyword, reportType: ReportType): KeywordTrend {
  const days = reportType === 'daily' ? 7 : reportType === 'weekly' ? 30 : 90
  const trendData = generateMockTrendData(days)
  
  const currentScore = trendData[trendData.length - 1].value
  const previousScore = trendData[Math.max(0, trendData.length - 2)].value
  const trendPercentage = Math.round(((currentScore - previousScore) / previousScore) * 100 * 100) / 100
  
  let trendDirection: 'up' | 'down' | 'stable'
  if (Math.abs(trendPercentage) < 2) {
    trendDirection = 'stable'
  } else if (trendPercentage > 0) {
    trendDirection = 'up'
  } else {
    trendDirection = 'down'
  }
  
  return {
    keyword_id: keyword.id,
    keyword_name: keyword.name,
    trend_direction: trendDirection,
    trend_percentage: Math.abs(trendPercentage),
    current_score: currentScore,
    previous_score: previousScore,
    data_points: trendData
  }
}

// 生成报告洞察
function generateInsights(keywordTrends: KeywordTrend[]): ReportInsight[] {
  const insights: ReportInsight[] = []
  
  // 找出表现最好的关键词
  const topPerformer = keywordTrends.reduce((best, current) => 
    current.current_score > best.current_score ? current : best
  )
  
  if (topPerformer.trend_direction === 'up') {
    insights.push({
      type: 'opportunity',
      title: '热门趋势发现',
      description: `"${topPerformer.keyword_name}" 表现突出，热度上升 ${topPerformer.trend_percentage}%，建议重点关注相关内容和市场动态。`,
      impact_level: 'high',
      keywords: [topPerformer.keyword_name]
    })
  }
  
  // 找出变化最大的关键词
  const biggestChange = keywordTrends.reduce((biggest, current) => 
    current.trend_percentage > biggest.trend_percentage ? current : biggest
  )
  
  if (biggestChange.trend_percentage > 10) {
    insights.push({
      type: biggestChange.trend_direction === 'up' ? 'trend' : 'warning',
      title: biggestChange.trend_direction === 'up' ? '快速增长趋势' : '关注度下降',
      description: `"${biggestChange.keyword_name}" 出现显著变化，${biggestChange.trend_direction === 'up' ? '增长' : '下降'} ${biggestChange.trend_percentage}%，需要密切关注。`,
      impact_level: biggestChange.trend_percentage > 20 ? 'high' : 'medium',
      keywords: [biggestChange.keyword_name]
    })
  }
  
  // 稳定趋势洞察
  const stableKeywords = keywordTrends.filter(k => k.trend_direction === 'stable')
  if (stableKeywords.length > 0) {
    insights.push({
      type: 'trend',
      title: '稳定发展趋势',
      description: `${stableKeywords.length} 个关键词保持稳定发展，显示出良好的市场基础和持续关注度。`,
      impact_level: 'low',
      keywords: stableKeywords.map(k => k.keyword_name)
    })
  }
  
  // 异常检测
  const anomalies = keywordTrends.filter(k => k.trend_percentage > 25)
  if (anomalies.length > 0) {
    insights.push({
      type: 'anomaly',
      title: '异常波动检测',
      description: `检测到 ${anomalies.length} 个关键词出现异常波动，可能受到突发事件或市场变化影响。`,
      impact_level: 'medium',
      keywords: anomalies.map(k => k.keyword_name)
    })
  }
  
  return insights
}

// 生成报告摘要
function generateSummary(keywordTrends: KeywordTrend[]): ReportSummary {
  const trendingUp = keywordTrends.filter(k => k.trend_direction === 'up').length
  const trendingDown = keywordTrends.filter(k => k.trend_direction === 'down').length
  const stable = keywordTrends.filter(k => k.trend_direction === 'stable').length
  
  const topPerformer = keywordTrends.reduce((best, current) => 
    current.current_score > best.current_score ? current : best
  )
  
  const biggestChange = keywordTrends.reduce((biggest, current) => 
    current.trend_percentage > biggest.trend_percentage ? current : biggest
  )
  
  let overallSentiment: 'positive' | 'negative' | 'neutral'
  if (trendingUp > trendingDown) {
    overallSentiment = 'positive'
  } else if (trendingDown > trendingUp) {
    overallSentiment = 'negative'
  } else {
    overallSentiment = 'neutral'
  }
  
  return {
    total_keywords: keywordTrends.length,
    trending_up: trendingUp,
    trending_down: trendingDown,
    stable: stable,
    top_performer: topPerformer.keyword_name,
    biggest_change: biggestChange.keyword_name,
    overall_sentiment: overallSentiment
  }
}

// 生成报告标题
function generateReportTitle(reportType: ReportType, keywords: Keyword[]): string {
  const date = new Date().toLocaleDateString('zh-CN')
  const keywordCount = keywords.length
  
  switch (reportType) {
    case 'daily':
      return `每日趋势报告 - ${date}`
    case 'weekly':
      return `周度分析报告 - ${date}`
    case 'monthly':
      return `月度全景报告 - ${date}`
    default:
      return `自定义趋势报告 - ${date}`
  }
}

// 主要的报告生成函数
export async function generateReport(
  keywords: Keyword[], 
  reportType: ReportType,
  customTitle?: string
): Promise<Omit<Report, 'id' | 'user_id' | 'created_at' | 'updated_at'>> {
  // 模拟AI处理时间
  await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))
  
  // 生成关键词趋势
  const keywordTrends = keywords.map(keyword => generateKeywordTrend(keyword, reportType))
  
  // 生成洞察
  const insights = generateInsights(keywordTrends)
  
  // 生成摘要
  const summary = generateSummary(keywordTrends)
  
  // 计算日期范围
  const endDate = new Date()
  const startDate = new Date()
  switch (reportType) {
    case 'daily':
      startDate.setDate(startDate.getDate() - 1)
      break
    case 'weekly':
      startDate.setDate(startDate.getDate() - 7)
      break
    case 'monthly':
      startDate.setMonth(startDate.getMonth() - 1)
      break
  }
  
  return {
    title: customTitle || generateReportTitle(reportType, keywords),
    description: `基于 ${keywords.length} 个关键词的${reportType === 'daily' ? '每日' : reportType === 'weekly' ? '周度' : '月度'}趋势分析报告`,
    type: reportType,
    status: 'completed',
    keywords: keywords.map(k => k.id),
    date_range: {
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0]
    },
    summary,
    keyword_trends: keywordTrends,
    insights,
    generated_at: new Date().toISOString()
  }
}

// 快速生成示例报告
export async function generateSampleReport(keywords: Keyword[]): Promise<Omit<Report, 'id' | 'user_id' | 'created_at' | 'updated_at'>> {
  return generateReport(keywords, 'daily', '示例趋势分析报告')
}
