import type { Keyword, CreateKeywordRequest, UpdateKeywordRequest } from '@/types/keyword'

// 模拟数据存储 - 在实际应用中这将是 Supabase 数据库
const STORAGE_KEY = 'baobao_keywords'

// 生成UUID的简单函数
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取存储的关键词
function getStoredKeywords(): Keyword[] {
  if (typeof window === 'undefined') return []
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch {
    return []
  }
}

// 保存关键词到存储
function saveKeywords(keywords: Keyword[]): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(keywords))
  } catch (error) {
    console.error('保存关键词失败:', error)
  }
}

export class MockKeywordService {
  static async getKeywords(userId: string): Promise<Keyword[]> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const allKeywords = getStoredKeywords()
    return allKeywords.filter(keyword => keyword.user_id === userId)
  }

  static async createKeyword(userId: string, keywordData: CreateKeywordRequest): Promise<Keyword> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const now = new Date().toISOString()
    const newKeyword: Keyword = {
      id: generateId(),
      user_id: userId,
      name: keywordData.name,
      description: keywordData.description,
      category: keywordData.category,
      is_active: true,
      created_at: now,
      updated_at: now,
    }

    const allKeywords = getStoredKeywords()
    
    // 检查是否已存在相同名称的关键词
    const existingKeyword = allKeywords.find(
      k => k.user_id === userId && k.name.toLowerCase() === keywordData.name.toLowerCase()
    )
    
    if (existingKeyword) {
      throw new Error('该关键词已存在')
    }

    allKeywords.push(newKeyword)
    saveKeywords(allKeywords)
    
    return newKeyword
  }

  static async updateKeyword(keywordId: string, updates: UpdateKeywordRequest): Promise<Keyword> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 400))
    
    const allKeywords = getStoredKeywords()
    const keywordIndex = allKeywords.findIndex(k => k.id === keywordId)
    
    if (keywordIndex === -1) {
      throw new Error('关键词不存在')
    }

    const updatedKeyword = {
      ...allKeywords[keywordIndex],
      ...updates,
      updated_at: new Date().toISOString(),
    }

    allKeywords[keywordIndex] = updatedKeyword
    saveKeywords(allKeywords)
    
    return updatedKeyword
  }

  static async deleteKeyword(keywordId: string): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const allKeywords = getStoredKeywords()
    const filteredKeywords = allKeywords.filter(k => k.id !== keywordId)
    
    if (filteredKeywords.length === allKeywords.length) {
      throw new Error('关键词不存在')
    }
    
    saveKeywords(filteredKeywords)
  }

  static async toggleKeywordStatus(keywordId: string, isActive: boolean): Promise<Keyword> {
    return this.updateKeyword(keywordId, { is_active: isActive })
  }

  static async getKeywordStats(userId: string) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const keywords = await this.getKeywords(userId)
    
    const totalKeywords = keywords.length
    const activeKeywords = keywords.filter(k => k.is_active).length
    const reportsGenerated = keywords.filter(k => k.last_report_generated_at).length
    const lastUpdate = keywords.length > 0 
      ? Math.max(...keywords.map(k => new Date(k.created_at).getTime()))
      : Date.now()

    return {
      total_keywords: totalKeywords,
      active_keywords: activeKeywords,
      reports_generated: reportsGenerated,
      last_update: new Date(lastUpdate).toISOString(),
    }
  }

  // 添加一些示例数据的方法
  static async addSampleData(userId: string): Promise<void> {
    const sampleKeywords = [
      {
        name: '人工智能大模型',
        description: '关注AI大模型的最新发展趋势和技术突破',
        category: '科技',
      },
      {
        name: '新能源汽车',
        description: '电动汽车市场动态和技术创新',
        category: '汽车',
      },
      {
        name: '区块链技术',
        description: '区块链在各行业的应用和发展',
        category: '科技',
      },
    ]

    for (const keyword of sampleKeywords) {
      try {
        await this.createKeyword(userId, keyword)
      } catch (error) {
        // 忽略重复关键词错误
        console.log(`跳过已存在的关键词: ${keyword.name}`)
      }
    }
  }
}
