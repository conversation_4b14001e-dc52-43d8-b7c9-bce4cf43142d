import { MockKeywordService } from './keywords-mock'
import type { Keyword, CreateKeywordRequest, UpdateKeywordRequest } from '@/types/keyword'

// 在开发环境中使用模拟服务，生产环境中可以切换到真实的 Supabase 服务
const USE_MOCK = true // 设置为 false 来使用真实的 Supabase 服务

export class KeywordService {
  static async getKeywords(userId: string): Promise<Keyword[]> {
    if (USE_MOCK) {
      return MockKeywordService.getKeywords(userId)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async createKeyword(userId: string, keywordData: CreateKeywordRequest): Promise<Keyword> {
    if (USE_MOCK) {
      return MockKeywordService.createKeyword(userId, keywordData)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async updateKeyword(keywordId: string, updates: UpdateKeywordRequest): Promise<Keyword> {
    if (USE_MOCK) {
      return MockKeywordService.updateKeyword(keywordId, updates)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async deleteKeyword(keywordId: string): Promise<void> {
    if (USE_MOCK) {
      return MockKeywordService.deleteKeyword(keywordId)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async toggleKeywordStatus(keywordId: string, isActive: boolean): Promise<Keyword> {
    if (USE_MOCK) {
      return MockKeywordService.toggleKeywordStatus(keywordId, isActive)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async getKeywordStats(userId: string) {
    if (USE_MOCK) {
      return MockKeywordService.getKeywordStats(userId)
    }

    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  // 添加示例数据的便捷方法
  static async addSampleData(userId: string): Promise<void> {
    if (USE_MOCK) {
      return MockKeywordService.addSampleData(userId)
    }

    throw new Error('示例数据功能仅在模拟模式下可用')
  }
}
