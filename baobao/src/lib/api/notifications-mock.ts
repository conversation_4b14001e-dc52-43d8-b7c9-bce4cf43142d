import type { 
  NotificationSettings, 
  CreateNotificationSettingsRequest, 
  UpdateNotificationSettingsRequest,
  NotificationPreview,
  NotificationFrequency,
  WeekDay
} from '@/types/notification'

// 模拟数据存储
const STORAGE_KEY = 'baobao_notification_settings'

// 生成UUID的简单函数
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取存储的推送设置
function getStoredSettings(): NotificationSettings[] {
  if (typeof window === 'undefined') return []
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch {
    return []
  }
}

// 保存推送设置到存储
function saveSettings(settings: NotificationSettings[]): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings))
  } catch (error) {
    console.error('保存推送设置失败:', error)
  }
}

// 计算下次发送时间
function calculateNextSendTime(settings: NotificationSettings): Date {
  const now = new Date()
  const [hours, minutes] = settings.time.split(':').map(Number)
  
  let nextSend = new Date()
  nextSend.setHours(hours, minutes, 0, 0)
  
  switch (settings.frequency) {
    case 'daily':
      // 如果今天的时间已过，则设置为明天
      if (nextSend <= now) {
        nextSend.setDate(nextSend.getDate() + 1)
      }
      break
      
    case 'weekly':
      if (settings.week_days && settings.week_days.length > 0) {
        // 找到下一个匹配的星期几
        const weekDayMap = {
          'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
          'thursday': 4, 'friday': 5, 'saturday': 6
        }
        
        const targetDays = settings.week_days.map(day => weekDayMap[day]).sort()
        const currentDay = now.getDay()
        
        let nextDay = targetDays.find(day => day > currentDay || (day === currentDay && nextSend > now))
        
        if (!nextDay) {
          // 如果本周没有合适的日期，取下周的第一个目标日期
          nextDay = targetDays[0]
          nextSend.setDate(nextSend.getDate() + (7 - currentDay + nextDay))
        } else {
          nextSend.setDate(nextSend.getDate() + (nextDay - currentDay))
        }
      }
      break
      
    case 'monthly':
      if (settings.month_day) {
        nextSend.setDate(settings.month_day)
        // 如果这个月的日期已过，设置为下个月
        if (nextSend <= now) {
          nextSend.setMonth(nextSend.getMonth() + 1)
        }
      }
      break
  }
  
  return nextSend
}

// 生成频率描述
function generateFrequencyDescription(settings: NotificationSettings): string {
  switch (settings.frequency) {
    case 'daily':
      return `每天 ${settings.time}`
      
    case 'weekly':
      if (settings.week_days && settings.week_days.length > 0) {
        const dayNames = {
          'monday': '周一', 'tuesday': '周二', 'wednesday': '周三',
          'thursday': '周四', 'friday': '周五', 'saturday': '周六', 'sunday': '周日'
        }
        const days = settings.week_days.map(day => dayNames[day]).join('、')
        return `每周 ${days} ${settings.time}`
      }
      return `每周 ${settings.time}`
      
    case 'monthly':
      return `每月 ${settings.month_day || 1} 日 ${settings.time}`
      
    default:
      return '自定义时间'
  }
}

export class MockNotificationService {
  static async getSettings(userId: string): Promise<NotificationSettings | null> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const allSettings = getStoredSettings()
    return allSettings.find(setting => setting.user_id === userId) || null
  }

  static async createSettings(
    userId: string, 
    settingsData: CreateNotificationSettingsRequest
  ): Promise<NotificationSettings> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const now = new Date().toISOString()
    const newSettings: NotificationSettings = {
      id: generateId(),
      user_id: userId,
      is_enabled: true,
      frequency: settingsData.frequency,
      time: settingsData.time,
      timezone: settingsData.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      week_days: settingsData.week_days,
      month_day: settingsData.month_day,
      keywords: settingsData.keywords || [],
      email_enabled: settingsData.email_enabled ?? true,
      push_enabled: settingsData.push_enabled ?? false,
      created_at: now,
      updated_at: now,
    }

    const allSettings = getStoredSettings()
    
    // 删除用户的旧设置（每个用户只能有一个设置）
    const filteredSettings = allSettings.filter(s => s.user_id !== userId)
    filteredSettings.push(newSettings)
    
    saveSettings(filteredSettings)
    return newSettings
  }

  static async updateSettings(
    settingsId: string, 
    updates: UpdateNotificationSettingsRequest
  ): Promise<NotificationSettings> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 400))
    
    const allSettings = getStoredSettings()
    const settingIndex = allSettings.findIndex(s => s.id === settingsId)
    
    if (settingIndex === -1) {
      throw new Error('推送设置不存在')
    }

    const updatedSettings = {
      ...allSettings[settingIndex],
      ...updates,
      updated_at: new Date().toISOString(),
    }

    allSettings[settingIndex] = updatedSettings
    saveSettings(allSettings)
    
    return updatedSettings
  }

  static async deleteSettings(settingsId: string): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const allSettings = getStoredSettings()
    const filteredSettings = allSettings.filter(s => s.id !== settingsId)
    
    if (filteredSettings.length === allSettings.length) {
      throw new Error('推送设置不存在')
    }
    
    saveSettings(filteredSettings)
  }

  static async getPreview(settings: NotificationSettings): Promise<NotificationPreview> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const nextSendTime = calculateNextSendTime(settings)
    const frequencyDescription = generateFrequencyDescription(settings)
    
    const deliveryMethods = []
    if (settings.email_enabled) deliveryMethods.push('邮件')
    if (settings.push_enabled) deliveryMethods.push('推送通知')
    
    return {
      next_send_time: nextSendTime.toISOString(),
      frequency_description: frequencyDescription,
      keywords_count: settings.keywords?.length || 0,
      delivery_methods: deliveryMethods,
    }
  }

  static async toggleSettings(settingsId: string, isEnabled: boolean): Promise<NotificationSettings> {
    return this.updateSettings(settingsId, { is_enabled: isEnabled })
  }
}
