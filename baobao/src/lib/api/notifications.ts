import { MockNotificationService } from './notifications-mock'
import type { 
  NotificationSettings, 
  CreateNotificationSettingsRequest, 
  UpdateNotificationSettingsRequest,
  NotificationPreview
} from '@/types/notification'

// 在开发环境中使用模拟服务
const USE_MOCK = true

export class NotificationService {
  static async getSettings(userId: string): Promise<NotificationSettings | null> {
    if (USE_MOCK) {
      return MockNotificationService.getSettings(userId)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async createSettings(
    userId: string, 
    settingsData: CreateNotificationSettingsRequest
  ): Promise<NotificationSettings> {
    if (USE_MOCK) {
      return MockNotificationService.createSettings(userId, settingsData)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async updateSettings(
    settingsId: string, 
    updates: UpdateNotificationSettingsRequest
  ): Promise<NotificationSettings> {
    if (USE_MOCK) {
      return MockNotificationService.updateSettings(settingsId, updates)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async deleteSettings(settingsId: string): Promise<void> {
    if (USE_MOCK) {
      return MockNotificationService.deleteSettings(settingsId)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async getPreview(settings: NotificationSettings): Promise<NotificationPreview> {
    if (USE_MOCK) {
      return MockNotificationService.getPreview(settings)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }

  static async toggleSettings(settingsId: string, isEnabled: boolean): Promise<NotificationSettings> {
    if (USE_MOCK) {
      return MockNotificationService.toggleSettings(settingsId, isEnabled)
    }
    
    // 真实的 Supabase 实现将在这里
    throw new Error('Supabase 服务尚未配置')
  }
}
