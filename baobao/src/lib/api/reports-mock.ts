import type { 
  Report, 
  CreateReportRequest, 
  ReportFilters, 
  ReportStats 
} from '@/types/report'
import type { Keyword } from '@/types/keyword'
import { generateReport } from '@/lib/ai/report-generator'

// 模拟数据存储
const STORAGE_KEY = 'baobao_reports'

// 生成UUID的简单函数
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取存储的报告
function getStoredReports(): Report[] {
  if (typeof window === 'undefined') return []
  
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch {
    return []
  }
}

// 保存报告到存储
function saveReports(reports: Report[]): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(reports))
  } catch (error) {
    console.error('保存报告失败:', error)
  }
}

export class MockReportService {
  static async getReports(userId: string, filters?: ReportFilters): Promise<Report[]> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    let reports = getStoredReports().filter(report => report.user_id === userId)
    
    // 应用过滤器
    if (filters) {
      if (filters.status) {
        reports = reports.filter(r => r.status === filters.status)
      }
      if (filters.type) {
        reports = reports.filter(r => r.type === filters.type)
      }
      if (filters.keyword_id) {
        reports = reports.filter(r => r.keywords.includes(filters.keyword_id))
      }
      if (filters.date_from) {
        reports = reports.filter(r => r.created_at >= filters.date_from!)
      }
      if (filters.date_to) {
        reports = reports.filter(r => r.created_at <= filters.date_to!)
      }
    }
    
    // 按创建时间倒序排列
    return reports.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  }

  static async getReport(reportId: string): Promise<Report | null> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const allReports = getStoredReports()
    return allReports.find(report => report.id === reportId) || null
  }

  static async createReport(
    userId: string, 
    keywords: Keyword[],
    reportData: CreateReportRequest
  ): Promise<Report> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const now = new Date().toISOString()
    
    // 创建初始报告记录
    const newReport: Report = {
      id: generateId(),
      user_id: userId,
      title: reportData.title || '生成中的报告',
      description: reportData.description,
      type: reportData.type,
      status: 'generating',
      keywords: reportData.keywords || keywords.map(k => k.id),
      date_range: reportData.date_range || {
        start_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: new Date().toISOString().split('T')[0]
      },
      summary: {
        total_keywords: 0,
        trending_up: 0,
        trending_down: 0,
        stable: 0,
        top_performer: '',
        biggest_change: '',
        overall_sentiment: 'neutral'
      },
      keyword_trends: [],
      insights: [],
      generated_at: '',
      created_at: now,
      updated_at: now,
    }

    const allReports = getStoredReports()
    allReports.push(newReport)
    saveReports(allReports)
    
    // 异步生成报告内容
    this.generateReportContent(newReport.id, keywords, reportData)
    
    return newReport
  }

  private static async generateReportContent(
    reportId: string,
    keywords: Keyword[],
    reportData: CreateReportRequest
  ): Promise<void> {
    try {
      // 生成报告内容
      const generatedContent = await generateReport(keywords, reportData.type, reportData.title)
      
      // 更新报告
      const allReports = getStoredReports()
      const reportIndex = allReports.findIndex(r => r.id === reportId)
      
      if (reportIndex !== -1) {
        allReports[reportIndex] = {
          ...allReports[reportIndex],
          ...generatedContent,
          status: 'completed',
          updated_at: new Date().toISOString()
        }
        saveReports(allReports)
      }
    } catch (error) {
      // 更新报告状态为失败
      const allReports = getStoredReports()
      const reportIndex = allReports.findIndex(r => r.id === reportId)
      
      if (reportIndex !== -1) {
        allReports[reportIndex] = {
          ...allReports[reportIndex],
          status: 'failed',
          error_message: error instanceof Error ? error.message : '生成报告失败',
          updated_at: new Date().toISOString()
        }
        saveReports(allReports)
      }
    }
  }

  static async deleteReport(reportId: string): Promise<void> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const allReports = getStoredReports()
    const filteredReports = allReports.filter(r => r.id !== reportId)
    
    if (filteredReports.length === allReports.length) {
      throw new Error('报告不存在')
    }
    
    saveReports(filteredReports)
  }

  static async getReportStats(userId: string): Promise<ReportStats> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 200))
    
    const reports = getStoredReports().filter(r => r.user_id === userId)
    
    const totalReports = reports.length
    const completedReports = reports.filter(r => r.status === 'completed').length
    const failedReports = reports.filter(r => r.status === 'failed').length
    
    const lastGenerated = reports.length > 0 
      ? Math.max(...reports.map(r => new Date(r.created_at).getTime()))
      : null
    
    // 模拟平均生成时间（3-8秒）
    const averageGenerationTime = 3 + Math.random() * 5
    
    return {
      total_reports: totalReports,
      completed_reports: completedReports,
      failed_reports: failedReports,
      last_generated: lastGenerated ? new Date(lastGenerated).toISOString() : null,
      average_generation_time: Math.round(averageGenerationTime)
    }
  }

  // 添加示例报告
  static async addSampleReports(userId: string, keywords: Keyword[]): Promise<void> {
    if (keywords.length === 0) return
    
    const sampleReports = [
      {
        title: '每日趋势摘要示例',
        type: 'daily' as const,
        description: '这是一个示例每日报告，展示关键词的24小时趋势变化'
      },
      {
        title: '周度分析报告示例',
        type: 'weekly' as const,
        description: '这是一个示例周度报告，提供深度的趋势分析和洞察'
      }
    ]

    for (const sample of sampleReports) {
      try {
        await this.createReport(userId, keywords, sample)
        // 等待一下避免ID冲突
        await new Promise(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.log(`跳过示例报告: ${sample.title}`)
      }
    }
  }
}
