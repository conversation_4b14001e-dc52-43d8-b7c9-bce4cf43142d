import { MockReportService } from './reports-mock'
import type { 
  Report, 
  CreateReportRequest, 
  ReportFilters, 
  ReportStats 
} from '@/types/report'
import type { Keyword } from '@/types/keyword'

// 在开发环境中使用模拟服务
const USE_MOCK = true

export class ReportService {
  static async getReports(userId: string, filters?: ReportFilters): Promise<Report[]> {
    if (USE_MOCK) {
      return MockReportService.getReports(userId, filters)
    }
    
    // 真实的 API 实现将在这里
    throw new Error('API 服务尚未配置')
  }

  static async getReport(reportId: string): Promise<Report | null> {
    if (USE_MOCK) {
      return MockReportService.getReport(reportId)
    }
    
    // 真实的 API 实现将在这里
    throw new Error('API 服务尚未配置')
  }

  static async createReport(
    userId: string, 
    keywords: Keyword[],
    reportData: CreateReportRequest
  ): Promise<Report> {
    if (USE_MOCK) {
      return MockReportService.createReport(userId, keywords, reportData)
    }
    
    // 真实的 API 实现将在这里
    throw new Error('API 服务尚未配置')
  }

  static async deleteReport(reportId: string): Promise<void> {
    if (USE_MOCK) {
      return MockReportService.deleteReport(reportId)
    }
    
    // 真实的 API 实现将在这里
    throw new Error('API 服务尚未配置')
  }

  static async getReportStats(userId: string): Promise<ReportStats> {
    if (USE_MOCK) {
      return MockReportService.getReportStats(userId)
    }
    
    // 真实的 API 实现将在这里
    throw new Error('API 服务尚未配置')
  }

  // 添加示例报告的便捷方法
  static async addSampleReports(userId: string, keywords: Keyword[]): Promise<void> {
    if (USE_MOCK) {
      return MockReportService.addSampleReports(userId, keywords)
    }
    
    throw new Error('示例数据功能仅在模拟模式下可用')
  }
}
