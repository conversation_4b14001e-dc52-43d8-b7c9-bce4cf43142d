'use client'

import { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { useReports } from '@/hooks/useReports'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendChart } from '@/components/charts/TrendChart'
import { ReportLoadingSkeleton } from '@/components/reports/ReportLoadingSkeleton'
import {
  ArrowLeft,
  Download,
  Share2,
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  Lightbulb,
  BarChart3,
  Eye,
  Bookmark,
  MoreHorizontal,
  Sparkles,
  Activity,
  PieChart,
  FileText
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { Report } from '@/types/report'
import { TREND_CONFIG, INSIGHT_CONFIG } from '@/types/report'

interface ReportPageProps {
  params: Promise<{
    id: string
  }>
}

export default function ReportPage({ params }: ReportPageProps) {
  const { id } = use(params)
  const router = useRouter()
  const { getReport } = useReports()
  const [report, setReport] = useState<Report | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchReport = async () => {
      try {
        setLoading(true)
        setError(null)
        const reportData = await getReport(id)
        setReport(reportData)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取报告失败')
      } finally {
        setLoading(false)
      }
    }

    fetchReport()
  }, [id, getReport])

  if (loading) {
    return <ReportLoadingSkeleton />
  }

  if (error || !report) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">报告不存在</h3>
            <p className="text-muted-foreground mb-4">
              {error || '找不到指定的报告'}
            </p>
            <Button onClick={() => router.back()}>
              返回
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="h-4 w-4" />
      case 'down': return <TrendingDown className="h-4 w-4" />
      default: return <Minus className="h-4 w-4" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-5 w-5 text-success" />
      case 'generating': return <Clock className="h-5 w-5 text-warning animate-spin" />
      case 'failed': return <AlertCircle className="h-5 w-5 text-destructive" />
      default: return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Enhanced Header */}
      <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Left Section */}
            <div className="flex items-start space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
                className="mt-1 hover:bg-brand-primary/10"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl lg:text-3xl font-bold text-foreground truncate">
                    {report.title}
                  </h1>
                  {getStatusIcon(report.status)}
                </div>

                {/* Enhanced Meta Information */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{format(new Date(report.created_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    <span>{report.keywords.length} 个关键词</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    <span>
                      {report.date_range.start_date} 至 {report.date_range.end_date}
                    </span>
                  </div>
                  {report.status === 'completed' && (
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      <span>阅读时间约 {Math.ceil(report.insights.length * 0.5)} 分钟</span>
                    </div>
                  )}
                </div>

                {/* Status Badge */}
                <div className="mt-3">
                  <Badge
                    variant={
                      report.status === 'completed' ? 'success' :
                      report.status === 'generating' ? 'warning' :
                      report.status === 'failed' ? 'destructive' : 'secondary'
                    }
                    className="text-xs"
                  >
                    {report.status === 'completed' && '✓ 分析完成'}
                    {report.status === 'generating' && '⏳ 生成中'}
                    {report.status === 'failed' && '✗ 生成失败'}
                    {report.status === 'pending' && '⏸ 等待中'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Right Section - Action Buttons */}
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="hidden lg:flex">
                <Bookmark className="h-4 w-4 mr-2" />
                收藏
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                分享
              </Button>
              <Button variant="brand" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出PDF
              </Button>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Status Messages */}
        {report.status === 'generating' && (
          <Card className="mb-8 border-warning/50 bg-warning/5">
            <CardContent className="p-6">
              <div className="flex items-center justify-center space-x-3">
                <div className="relative">
                  <Clock className="h-6 w-6 text-warning animate-spin" />
                  <div className="absolute inset-0 rounded-full border-2 border-warning/20 animate-pulse"></div>
                </div>
                <div>
                  <p className="text-warning font-medium">AI 正在分析您的数据</p>
                  <p className="text-warning/80 text-sm">预计还需要 2-3 分钟，请稍候...</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {report.status === 'failed' && (
          <Card className="mb-8 border-destructive/50 bg-destructive/5">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <AlertCircle className="h-6 w-6 text-destructive" />
                <div>
                  <p className="text-destructive font-medium">报告生成失败</p>
                  <p className="text-destructive/80 text-sm">
                    {report.error_message || '系统遇到了一些问题，请稍后重试'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {report.status === 'completed' && (
          <div className="space-y-12">
            {/* Enhanced Report Summary */}
            <section>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground mb-2 flex items-center gap-3">
                  <div className="p-2 bg-brand-primary/10 rounded-lg">
                    <PieChart className="h-6 w-6 text-brand-primary" />
                  </div>
                  数据概览
                </h2>
                <p className="text-muted-foreground text-lg">
                  {report.description || '基于您关注的关键词生成的综合趋势分析'}
                </p>
              </div>

              {/* Key Metrics Grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <Card className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">关键词总数</p>
                        <p className="text-3xl font-bold text-foreground">{report.summary.total_keywords}</p>
                      </div>
                      <div className="p-3 bg-brand-primary/10 rounded-full">
                        <Target className="h-6 w-6 text-brand-primary" />
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-brand-primary/20"></div>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">上升趋势</p>
                        <p className="text-3xl font-bold text-success">{report.summary.trending_up}</p>
                      </div>
                      <div className="p-3 bg-success/10 rounded-full">
                        <TrendingUp className="h-6 w-6 text-success" />
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-success/20"></div>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">下降趋势</p>
                        <p className="text-3xl font-bold text-destructive">{report.summary.trending_down}</p>
                      </div>
                      <div className="p-3 bg-destructive/10 rounded-full">
                        <TrendingDown className="h-6 w-6 text-destructive" />
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-destructive/20"></div>
                  </CardContent>
                </Card>

                <Card className="relative overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-1">稳定趋势</p>
                        <p className="text-3xl font-bold text-muted-foreground">{report.summary.stable}</p>
                      </div>
                      <div className="p-3 bg-muted/20 rounded-full">
                        <Minus className="h-6 w-6 text-muted-foreground" />
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-muted/20"></div>
                  </CardContent>
                </Card>
              </div>

              {/* Highlight Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="border-success/20 bg-gradient-to-br from-success/5 to-success/10">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-3 bg-success/20 rounded-full">
                        <Sparkles className="h-6 w-6 text-success" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-success mb-2">🏆 表现最佳关键词</h4>
                        <p className="text-lg font-medium text-foreground">{report.summary.top_performer}</p>
                        <p className="text-sm text-muted-foreground mt-1">在分析期间表现最为突出</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-info/20 bg-gradient-to-br from-info/5 to-info/10">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-3 bg-info/20 rounded-full">
                        <Activity className="h-6 w-6 text-info" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-info mb-2">📈 变化最大关键词</h4>
                        <p className="text-lg font-medium text-foreground">{report.summary.biggest_change}</p>
                        <p className="text-sm text-muted-foreground mt-1">波动幅度最为显著</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Overall Sentiment */}
              <Card className="mt-6">
                <CardContent className="p-6">
                  <div className="flex items-center justify-center gap-4">
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground mb-2">整体市场情绪</p>
                      <Badge
                        variant={
                          report.summary.overall_sentiment === 'positive' ? 'success' :
                          report.summary.overall_sentiment === 'negative' ? 'destructive' : 'secondary'
                        }
                        className="text-lg px-4 py-2"
                      >
                        {report.summary.overall_sentiment === 'positive' && '😊 积极乐观'}
                        {report.summary.overall_sentiment === 'negative' && '😟 谨慎观望'}
                        {report.summary.overall_sentiment === 'neutral' && '😐 中性平稳'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Enhanced Keyword Trends */}
            <section>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground mb-2 flex items-center gap-3">
                  <div className="p-2 bg-brand-primary/10 rounded-lg">
                    <BarChart3 className="h-6 w-6 text-brand-primary" />
                  </div>
                  关键词趋势分析
                </h2>
                <p className="text-muted-foreground">
                  深度分析每个关键词的表现趋势和变化模式
                </p>
              </div>

              <div className="grid gap-8">
                {report.keyword_trends.map((trend, index) => {
                  const config = TREND_CONFIG[trend.trend_direction]
                  const trendColor = trend.trend_direction === 'up' ? '#10b981' :
                                   trend.trend_direction === 'down' ? '#ef4444' : '#64748b'

                  return (
                    <Card key={trend.keyword_id} className="overflow-hidden">
                      <CardHeader className="pb-4">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-3">
                              <div className={`w-3 h-3 rounded-full ${config.bgColor}`}></div>
                              <h3 className="text-xl font-semibold text-foreground">
                                {trend.keyword_name}
                              </h3>
                            </div>
                            <Badge
                              variant="outline"
                              className={`${config.color} border-current`}
                            >
                              <span className="mr-1">{config.icon}</span>
                              {config.label} {trend.trend_percentage.toFixed(1)}%
                            </Badge>
                          </div>

                          <div className="flex items-center gap-6 text-sm">
                            <div className="text-center">
                              <p className="text-muted-foreground">当前分数</p>
                              <p className="text-lg font-bold text-foreground">
                                {trend.current_score.toFixed(1)}
                              </p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">前期分数</p>
                              <p className="text-lg font-bold text-muted-foreground">
                                {trend.previous_score.toFixed(1)}
                              </p>
                            </div>
                            <div className="text-center">
                              <p className="text-muted-foreground">变化幅度</p>
                              <p className={`text-lg font-bold ${config.color}`}>
                                {trend.trend_direction === 'up' ? '+' : trend.trend_direction === 'down' ? '-' : ''}
                                {trend.trend_percentage.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        {/* Trend Chart */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-muted-foreground mb-3">
                            趋势变化图表
                          </h4>
                          <div className="bg-muted/20 rounded-lg p-4">
                            <TrendChart
                              data={trend.data_points.map(point => ({
                                date: point.date,
                                value: point.value,
                                name: trend.keyword_name
                              }))}
                              height={200}
                              color={trendColor}
                              showGrid={true}
                            />
                          </div>
                        </div>

                        {/* Performance Indicators */}
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="p-3 bg-muted/30 rounded-lg text-center">
                            <p className="text-xs text-muted-foreground mb-1">最高值</p>
                            <p className="font-semibold">
                              {Math.max(...trend.data_points.map(p => p.value)).toFixed(1)}
                            </p>
                          </div>
                          <div className="p-3 bg-muted/30 rounded-lg text-center">
                            <p className="text-xs text-muted-foreground mb-1">最低值</p>
                            <p className="font-semibold">
                              {Math.min(...trend.data_points.map(p => p.value)).toFixed(1)}
                            </p>
                          </div>
                          <div className="p-3 bg-muted/30 rounded-lg text-center">
                            <p className="text-xs text-muted-foreground mb-1">平均值</p>
                            <p className="font-semibold">
                              {(trend.data_points.reduce((sum, p) => sum + p.value, 0) / trend.data_points.length).toFixed(1)}
                            </p>
                          </div>
                          <div className="p-3 bg-muted/30 rounded-lg text-center">
                            <p className="text-xs text-muted-foreground mb-1">波动性</p>
                            <p className="font-semibold">
                              {trend.trend_percentage > 15 ? '高' : trend.trend_percentage > 5 ? '中' : '低'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </section>

            {/* Enhanced AI Insights */}
            <section>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-foreground mb-2 flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-lg">
                    <Lightbulb className="h-6 w-6 text-yellow-600" />
                  </div>
                  AI 智能洞察
                </h2>
                <p className="text-muted-foreground">
                  基于数据分析生成的专业洞察和建议
                </p>
              </div>

              <div className="grid gap-6">
                {report.insights.map((insight, index) => {
                  const config = INSIGHT_CONFIG[insight.type]
                  const isHighImpact = insight.impact_level === 'high'

                  return (
                    <Card
                      key={index}
                      className={`relative overflow-hidden transition-all duration-200 hover:shadow-lg ${
                        isHighImpact ? 'border-l-4 border-l-yellow-500' : ''
                      }`}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          {/* Icon */}
                          <div className={`p-3 rounded-full ${config.bgColor} flex-shrink-0`}>
                            <span className="text-2xl">{config.icon}</span>
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            {/* Header */}
                            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 mb-3">
                              <h3 className={`text-lg font-semibold ${config.color}`}>
                                {insight.title}
                              </h3>
                              <div className="flex items-center gap-2">
                                <Badge
                                  variant={
                                    insight.impact_level === 'high' ? 'destructive' :
                                    insight.impact_level === 'medium' ? 'warning' : 'secondary'
                                  }
                                  size="sm"
                                >
                                  {insight.impact_level === 'high' && '🔥 高影响'}
                                  {insight.impact_level === 'medium' && '⚡ 中影响'}
                                  {insight.impact_level === 'low' && '💡 低影响'}
                                </Badge>
                                <Badge variant="outline" size="sm" className={config.color}>
                                  {config.label}
                                </Badge>
                              </div>
                            </div>

                            {/* Description */}
                            <p className="text-foreground leading-relaxed mb-4 text-base">
                              {insight.description}
                            </p>

                            {/* Related Keywords */}
                            <div className="space-y-2">
                              <p className="text-sm font-medium text-muted-foreground">
                                相关关键词：
                              </p>
                              <div className="flex flex-wrap gap-2">
                                {insight.keywords.map((keyword, idx) => (
                                  <Badge
                                    key={idx}
                                    variant="secondary"
                                    className="bg-muted/50 hover:bg-muted transition-colors"
                                  >
                                    <Target className="h-3 w-3 mr-1" />
                                    {keyword}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* High Impact Indicator */}
                        {isHighImpact && (
                          <div className="absolute top-4 right-4">
                            <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )
                })}
              </div>

              {/* Insights Summary */}
              <Card className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-blue-100 rounded-full">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-blue-900 mb-1">
                        洞察总结
                      </h3>
                      <p className="text-blue-700 text-sm">
                        本次分析共发现 {report.insights.length} 个关键洞察，其中
                        {report.insights.filter(i => i.impact_level === 'high').length} 个高影响洞察需要重点关注。
                        建议根据这些洞察调整您的关键词策略。
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
            {/* Report Actions & Navigation */}
            <section className="mt-16">
              <Card className="bg-gradient-to-r from-brand-primary/5 to-brand-secondary/5 border-brand-primary/20">
                <CardContent className="p-8">
                  <div className="text-center space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        📊 报告分析完成
                      </h3>
                      <p className="text-muted-foreground">
                        希望这份报告能为您的决策提供有价值的参考
                      </p>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                      <Button variant="brand" size="lg" className="min-w-[140px]">
                        <Download className="h-4 w-4 mr-2" />
                        下载PDF报告
                      </Button>
                      <Button variant="outline" size="lg" className="min-w-[140px]">
                        <Share2 className="h-4 w-4 mr-2" />
                        分享报告
                      </Button>
                      <Button variant="ghost" size="lg" className="min-w-[140px]">
                        <Bookmark className="h-4 w-4 mr-2" />
                        收藏报告
                      </Button>
                    </div>

                    <div className="pt-4 border-t border-border/50">
                      <p className="text-sm text-muted-foreground mb-3">
                        继续探索更多功能
                      </p>
                      <div className="flex flex-wrap items-center justify-center gap-3">
                        <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/reports')}>
                          <BarChart3 className="h-4 w-4 mr-2" />
                          查看所有报告
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard')}>
                          <Target className="h-4 w-4 mr-2" />
                          管理关键词
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/notifications')}>
                          <Clock className="h-4 w-4 mr-2" />
                          设置推送
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </section>
          </div>
        )}

        {/* Footer */}
        <footer className="mt-16 py-8 border-t border-border/50">
          <div className="text-center text-sm text-muted-foreground">
            <p>由 报报 AI 智能分析引擎生成 • 数据更新时间：{format(new Date(), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}</p>
          </div>
        </footer>
      </main>
    </div>
  )
}
