'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useReports } from '@/hooks/useReports'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  Lightbulb,
  BarChart3
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { Report } from '@/types/report'
import { TREND_CONFIG, INSIGHT_CONFIG } from '@/types/report'

interface ReportPageProps {
  params: {
    id: string
  }
}

export default function ReportPage({ params }: ReportPageProps) {
  const router = useRouter()
  const { getReport } = useReports()
  const [report, setReport] = useState<Report | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchReport = async () => {
      try {
        setLoading(true)
        setError(null)
        const reportData = await getReport(params.id)
        setReport(reportData)
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取报告失败')
      } finally {
        setLoading(false)
      }
    }

    fetchReport()
  }, [params.id, getReport])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">加载报告中...</p>
        </div>
      </div>
    )
  }

  if (error || !report) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">报告不存在</h3>
            <p className="text-muted-foreground mb-4">
              {error || '找不到指定的报告'}
            </p>
            <Button onClick={() => router.back()}>
              返回
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="h-4 w-4" />
      case 'down': return <TrendingDown className="h-4 w-4" />
      default: return <Minus className="h-4 w-4" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-5 w-5 text-success" />
      case 'generating': return <Clock className="h-5 w-5 text-warning animate-spin" />
      case 'failed': return <AlertCircle className="h-5 w-5 text-destructive" />
      default: return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">{report.title}</h1>
                <div className="flex items-center space-x-4 mt-1">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(report.status)}
                    <span className="text-sm text-muted-foreground">
                      {report.status === 'completed' && '已完成'}
                      {report.status === 'generating' && '生成中'}
                      {report.status === 'failed' && '生成失败'}
                      {report.status === 'pending' && '等待中'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {format(new Date(report.created_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                分享
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {report.status === 'generating' && (
          <Card className="mb-6 border-warning">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-warning animate-spin" />
                <p className="text-warning text-sm">
                  报告正在生成中，请稍候...
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {report.status === 'failed' && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <p className="text-destructive text-sm">
                  报告生成失败：{report.error_message || '未知错误'}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {report.status === 'completed' && (
          <div className="space-y-8">
            {/* 报告摘要 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  报告摘要
                </CardTitle>
                <CardDescription>
                  {report.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-foreground">{report.summary.total_keywords}</p>
                    <p className="text-sm text-muted-foreground">关键词总数</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-success">{report.summary.trending_up}</p>
                    <p className="text-sm text-muted-foreground">上升趋势</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-destructive">{report.summary.trending_down}</p>
                    <p className="text-sm text-muted-foreground">下降趋势</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-muted-foreground">{report.summary.stable}</p>
                    <p className="text-sm text-muted-foreground">稳定趋势</p>
                  </div>
                </div>
                
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-success/10 rounded-lg">
                    <h4 className="font-medium text-success mb-1">表现最佳</h4>
                    <p className="text-sm">{report.summary.top_performer}</p>
                  </div>
                  <div className="p-4 bg-info/10 rounded-lg">
                    <h4 className="font-medium text-info mb-1">变化最大</h4>
                    <p className="text-sm">{report.summary.biggest_change}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 关键词趋势 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  关键词趋势分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.keyword_trends.map((trend) => {
                    const config = TREND_CONFIG[trend.trend_direction]
                    return (
                      <div key={trend.keyword_id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{trend.keyword_name}</h4>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className={`${config.color} ${config.bgColor}`}>
                              <span className="mr-1">{config.icon}</span>
                              {config.label} {trend.trend_percentage.toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">当前分数：</span>
                            <span className="font-medium ml-1">{trend.current_score.toFixed(1)}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">前期分数：</span>
                            <span className="font-medium ml-1">{trend.previous_score.toFixed(1)}</span>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* 洞察分析 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  智能洞察
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.insights.map((insight, index) => {
                    const config = INSIGHT_CONFIG[insight.type]
                    return (
                      <div key={index} className={`p-4 rounded-lg border ${config.bgColor}`}>
                        <div className="flex items-start space-x-3">
                          <span className="text-2xl">{config.icon}</span>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className={`font-medium ${config.color}`}>{insight.title}</h4>
                              <Badge variant="outline" size="sm">
                                {insight.impact_level === 'high' && '高影响'}
                                {insight.impact_level === 'medium' && '中影响'}
                                {insight.impact_level === 'low' && '低影响'}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {insight.description}
                            </p>
                            <div className="flex flex-wrap gap-1">
                              {insight.keywords.map((keyword, idx) => (
                                <Badge key={idx} variant="secondary" size="sm">
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  )
}
