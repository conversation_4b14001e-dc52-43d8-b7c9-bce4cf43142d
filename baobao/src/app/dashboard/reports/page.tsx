'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useKeywords } from '@/hooks/useKeywords'
import { useReports } from '@/hooks/useReports'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  TrendingUp, 
  ArrowLeft, 
  Plus, 
  Search,
  Calendar,
  BarChart3,
  FileText,
  Trash2,
  Eye,
  Download,
  Filter,
  CheckCircle,
  Clock,
  AlertCircle,
  Sparkles
} from 'lucide-react'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import type { CreateReportRequest, ReportType } from '@/types/report'
import { REPORT_TEMPLATES } from '@/types/report'

export default function ReportsPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const { keywords, loading: keywordsLoading } = useKeywords()
  const { 
    reports, 
    stats, 
    loading: reportsLoading, 
    error,
    pendingReports,
    createReport,
    deleteReport,
    addSampleReports
  } = useReports()

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [isCreating, setIsCreating] = useState(false)
  const [isAddingSample, setIsAddingSample] = useState(false)

  if (authLoading || keywordsLoading || reportsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  const activeKeywords = keywords.filter(k => k.is_active)

  // 过滤报告
  const filteredReports = reports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || report.status === statusFilter
    const matchesType = !typeFilter || report.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  const handleCreateReport = async (template: typeof REPORT_TEMPLATES[0]) => {
    if (activeKeywords.length === 0) {
      alert('请先添加并启用关键词')
      return
    }

    setIsCreating(true)
    try {
      const reportData: CreateReportRequest = {
        type: template.type,
        title: `${template.name} - ${new Date().toLocaleDateString('zh-CN')}`
      }
      await createReport(activeKeywords, reportData)
    } catch (error) {
      console.error('创建报告失败:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteReport = async (reportId: string) => {
    if (confirm('确定要删除这个报告吗？此操作无法撤销。')) {
      try {
        await deleteReport(reportId)
      } catch (error) {
        console.error('删除报告失败:', error)
      }
    }
  }

  const handleAddSampleReports = async () => {
    if (activeKeywords.length === 0) {
      alert('请先添加关键词')
      return
    }

    setIsAddingSample(true)
    try {
      await addSampleReports(activeKeywords)
    } catch (error) {
      console.error('添加示例报告失败:', error)
    } finally {
      setIsAddingSample(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-success" />
      case 'generating': return <Clock className="h-4 w-4 text-warning animate-spin" />
      case 'failed': return <AlertCircle className="h-4 w-4 text-destructive" />
      default: return <Clock className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'generating': return '生成中'
      case 'failed': return '失败'
      default: return '等待中'
    }
  }

  const getTypeLabel = (type: ReportType) => {
    switch (type) {
      case 'daily': return '每日报告'
      case 'weekly': return '周度报告'
      case 'monthly': return '月度报告'
      default: return '自定义报告'
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-6 w-6 text-brand-primary" />
                <h1 className="text-2xl font-bold text-foreground">趋势分析报告</h1>
                <Badge variant="brand">Beta</Badge>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题和描述 */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            智能报告中心 📊
          </h2>
          <p className="text-muted-foreground text-lg">
            生成和管理关键词的趋势分析报告，获得深度洞察
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <p className="text-destructive text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总报告数</p>
                  <p className="text-2xl font-bold">{stats?.total_reports || 0}</p>
                </div>
                <FileText className="h-8 w-8 text-brand-primary" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已完成</p>
                  <p className="text-2xl font-bold">{stats?.completed_reports || 0}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-success" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">生成中</p>
                  <p className="text-2xl font-bold">{pendingReports}</p>
                </div>
                <Clock className="h-8 w-8 text-warning" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">失败</p>
                  <p className="text-2xl font-bold">{stats?.failed_reports || 0}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-destructive" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 报告列表 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 搜索和过滤 */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索报告..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-input rounded-md bg-background text-sm"
                >
                  <option value="">所有状态</option>
                  <option value="completed">已完成</option>
                  <option value="generating">生成中</option>
                  <option value="failed">失败</option>
                </select>
                
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-input rounded-md bg-background text-sm"
                >
                  <option value="">所有类型</option>
                  <option value="daily">每日报告</option>
                  <option value="weekly">周度报告</option>
                  <option value="monthly">月度报告</option>
                </select>
              </div>
            </div>

            {/* 报告列表 */}
            {filteredReports.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {reports.length === 0 ? '还没有报告' : '没有找到匹配的报告'}
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    {reports.length === 0 
                      ? '生成您的第一个趋势分析报告'
                      : '尝试调整搜索条件或过滤器'
                    }
                  </p>
                  {reports.length === 0 && activeKeywords.length > 0 && (
                    <Button 
                      variant="brand" 
                      onClick={handleAddSampleReports}
                      disabled={isAddingSample}
                    >
                      <Sparkles className="h-4 w-4 mr-2" />
                      {isAddingSample ? '添加中...' : '添加示例报告'}
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredReports.map((report) => (
                  <Card key={report.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-3">
                            <h3 className="font-semibold text-lg">{report.title}</h3>
                            <Badge variant="outline">{getTypeLabel(report.type)}</Badge>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(report.status)}
                              <span className="text-sm text-muted-foreground">
                                {getStatusLabel(report.status)}
                              </span>
                            </div>
                          </div>

                          {report.description && (
                            <p className="text-muted-foreground text-sm">
                              {report.description}
                            </p>
                          )}

                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              创建于 {format(new Date(report.created_at), 'MM月dd日 HH:mm', { locale: zhCN })}
                            </div>
                            <div>
                              包含 {report.keywords.length} 个关键词
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          {report.status === 'completed' && (
                            <>
                              <Link href={`/dashboard/reports/${report.id}`}>
                                <Button variant="outline" size="sm">
                                  <Eye className="h-4 w-4 mr-2" />
                                  查看
                                </Button>
                              </Link>
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeleteReport(report.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* 侧边栏 - 创建报告 */}
          <div className="space-y-6">
            {/* 快速创建 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  创建新报告
                </CardTitle>
                <CardDescription>
                  选择报告模板快速生成分析报告
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {activeKeywords.length > 0 ? (
                  REPORT_TEMPLATES.map((template) => (
                    <Button
                      key={template.id}
                      variant="outline"
                      className="w-full justify-start h-auto p-4"
                      onClick={() => handleCreateReport(template)}
                      disabled={isCreating}
                    >
                      <div className="text-left">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-lg">{template.icon}</span>
                          <span className="font-medium">{template.name}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {template.description}
                        </p>
                      </div>
                    </Button>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      需要先添加关键词
                    </p>
                    <Link href="/dashboard">
                      <Button variant="brand" size="sm">
                        添加关键词
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 使用说明 */}
            <Card>
              <CardHeader>
                <CardTitle>使用说明</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <h4 className="font-medium mb-1">📊 报告类型</h4>
                  <p className="text-muted-foreground">
                    每日、周度、月度报告提供不同时间维度的趋势分析
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">🤖 AI 分析</h4>
                  <p className="text-muted-foreground">
                    系统自动分析关键词趋势并提供智能洞察
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">📈 数据可视化</h4>
                  <p className="text-muted-foreground">
                    直观的图表和数据展示，便于理解趋势变化
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
