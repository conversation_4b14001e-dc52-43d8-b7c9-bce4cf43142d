'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useKeywords } from '@/hooks/useKeywords'
import { useNotifications } from '@/hooks/useNotifications'
import { useReports } from '@/hooks/useReports'
import { KeywordService } from '@/lib/api/keywords'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AddKeywordDialog } from '@/components/keywords/AddKeywordDialog'
import { KeywordList } from '@/components/keywords/KeywordList'
import { DashboardOverview } from '@/components/dashboard/DashboardOverview'
import { MobileNav } from '@/components/layout/MobileNav'
import { TrendingUp, LogOut, User, Settings, Plus, BarChart3, Clock, Target, Sparkles, Bell, CheckCircle, AlertCircle } from 'lucide-react'
import type { CreateKeywordRequest } from '@/types/keyword'

export default function DashboardPage() {
  const { user, signOut, loading: authLoading } = useAuth()
  const {
    keywords,
    stats,
    loading: keywordsLoading,
    error: keywordsError,
    createKeyword,
    deleteKeyword,
    toggleKeywordStatus
  } = useKeywords()

  const {
    settings: notificationSettings,
    loading: notificationsLoading
  } = useNotifications()

  const {
    reports,
    stats: reportStats,
    loading: reportsLoading,
    pendingReports,
    recentReports,
    createReport,
    addSampleReports
  } = useReports()

  const [showAddDialog, setShowAddDialog] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [isAddingSample, setIsAddingSample] = useState(false)
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const [isAddingSampleReports, setIsAddingSampleReports] = useState(false)

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <TrendingUp className="h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const handleCreateKeyword = async (data: CreateKeywordRequest) => {
    setIsCreating(true)
    try {
      await createKeyword(data)
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteKeyword = async (keywordId: string) => {
    if (confirm('确定要删除这个关键词吗？此操作无法撤销。')) {
      try {
        await deleteKeyword(keywordId)
      } catch (error) {
        console.error('删除关键词失败:', error)
      }
    }
  }

  const handleAddSampleData = async () => {
    if (!user?.id) return

    setIsAddingSample(true)
    try {
      await KeywordService.addSampleData(user.id)
      // 刷新关键词列表
      window.location.reload()
    } catch (error) {
      console.error('添加示例数据失败:', error)
    } finally {
      setIsAddingSample(false)
    }
  }

  const handleGenerateReport = async () => {
    const activeKeywords = keywords.filter(k => k.is_active)
    if (activeKeywords.length === 0) {
      alert('请先添加并启用关键词')
      return
    }

    setIsGeneratingReport(true)
    try {
      await createReport(activeKeywords, {
        type: 'daily',
        title: `每日趋势报告 - ${new Date().toLocaleDateString('zh-CN')}`
      })
    } catch (error) {
      console.error('生成报告失败:', error)
    } finally {
      setIsGeneratingReport(false)
    }
  }

  const handleAddSampleReports = async () => {
    const activeKeywords = keywords.filter(k => k.is_active)
    if (activeKeywords.length === 0) {
      alert('请先添加关键词')
      return
    }

    setIsAddingSampleReports(true)
    try {
      await addSampleReports(activeKeywords)
    } catch (error) {
      console.error('添加示例报告失败:', error)
    } finally {
      setIsAddingSampleReports(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-brand-primary" />
              <h1 className="text-2xl font-bold text-foreground">报报</h1>
              <Badge variant="brand">Beta</Badge>
            </div>
            <div className="flex items-center space-x-4">
              {/* Desktop navigation */}
              <div className="hidden md:flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {user?.name || user?.email}
                  </span>
                </div>
                <Button variant="ghost" size="icon">
                  <Settings className="h-5 w-5" />
                </Button>
                <Button variant="outline" onClick={handleSignOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  登出
                </Button>
              </div>

              {/* Mobile navigation */}
              <MobileNav />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-2">
                欢迎回来，{user?.name || '用户'}！ 👋
              </h2>
              <p className="text-muted-foreground text-base lg:text-lg">
                这里是您的个人仪表板，管理关键词和查看趋势报告
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              {keywords.length === 0 && (
                <Button
                  variant="outline"
                  onClick={handleAddSampleData}
                  disabled={isAddingSample}
                  className="w-full sm:w-auto"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  {isAddingSample ? '添加中...' : '添加示例数据'}
                </Button>
              )}
              <Button
                variant="brand"
                onClick={() => setShowAddDialog(true)}
                className="w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加关键词
              </Button>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {keywordsError && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{keywordsError}</p>
            </CardContent>
          </Card>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总关键词</p>
                  <p className="text-2xl font-bold">{stats?.total_keywords || 0}</p>
                </div>
                <Target className="h-8 w-8 text-brand-primary" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">活跃关键词</p>
                  <p className="text-2xl font-bold">{stats?.active_keywords || 0}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-success" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">生成报告</p>
                  <p className="text-2xl font-bold">{reportStats?.completed_reports || 0}</p>
                  {pendingReports > 0 && (
                    <p className="text-xs text-warning">
                      {pendingReports} 个生成中
                    </p>
                  )}
                </div>
                <BarChart3 className="h-8 w-8 text-info" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">推送设置</p>
                  <p className="text-sm font-medium">
                    {notificationSettings?.is_enabled ? '已启用' : '未设置'}
                  </p>
                </div>
                {notificationSettings?.is_enabled ? (
                  <CheckCircle className="h-8 w-8 text-success" />
                ) : (
                  <AlertCircle className="h-8 w-8 text-warning" />
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 关键词列表 */}
        <Card>
          <CardHeader>
            <CardTitle>我的关键词</CardTitle>
            <CardDescription>
              管理您关注的关键词，系统将为活跃的关键词生成趋势分析报告
            </CardDescription>
          </CardHeader>
          <CardContent>
            <KeywordList
              keywords={keywords}
              loading={keywordsLoading}
              onDelete={handleDeleteKeyword}
              onToggleStatus={toggleKeywordStatus}
            />
          </CardContent>
        </Card>

        {/* 数据概览 */}
        {keywords.length > 0 && (
          <div className="mt-8">
            <h3 className="text-2xl font-bold text-foreground mb-6">数据概览</h3>
            <DashboardOverview
              keywords={keywords}
              reports={reports}
              loading={keywordsLoading || reportsLoading}
            />
          </div>
        )}

        {/* 推送设置卡片 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              推送设置
            </CardTitle>
            <CardDescription>
              配置报告的推送时间和频率
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="space-y-2 flex-1">
                {notificationSettings ? (
                  <div>
                    <div className="flex items-center gap-2">
                      {notificationSettings.is_enabled ? (
                        <CheckCircle className="h-4 w-4 text-success" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-warning" />
                      )}
                      <span className="text-sm font-medium">
                        {notificationSettings.is_enabled ? '推送已启用' : '推送已停用'}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notificationSettings.frequency === 'daily' && '每日推送'}
                      {notificationSettings.frequency === 'weekly' && '每周推送'}
                      {notificationSettings.frequency === 'monthly' && '每月推送'}
                      {notificationSettings.time && ` · ${notificationSettings.time}`}
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm font-medium">未设置推送</p>
                    <p className="text-sm text-muted-foreground">
                      设置定时推送，及时获得趋势分析报告
                    </p>
                  </div>
                )}
              </div>
              <Link href="/dashboard/notifications">
                <Button
                  variant={notificationSettings ? "outline" : "brand"}
                  className="w-full sm:w-auto"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {notificationSettings ? '管理设置' : '设置推送'}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* 报告管理卡片 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              趋势分析报告
            </CardTitle>
            <CardDescription>
              生成和管理关键词的趋势分析报告
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 报告统计 */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-info">{reportStats?.total_reports || 0}</p>
                  <p className="text-xs text-muted-foreground">总报告数</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-success">{reportStats?.completed_reports || 0}</p>
                  <p className="text-xs text-muted-foreground">已完成</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-warning">{pendingReports}</p>
                  <p className="text-xs text-muted-foreground">生成中</p>
                </div>
              </div>

              {/* 最近报告 */}
              {recentReports.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">最近报告</h4>
                  <div className="space-y-2">
                    {recentReports.slice(0, 3).map((report) => (
                      <div key={report.id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                        <div>
                          <p className="text-sm font-medium">{report.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(report.created_at).toLocaleDateString('zh-CN')}
                          </p>
                        </div>
                        <Badge
                          variant={
                            report.status === 'completed' ? 'success' :
                            report.status === 'generating' ? 'warning' :
                            report.status === 'failed' ? 'destructive' : 'secondary'
                          }
                        >
                          {report.status === 'completed' && '已完成'}
                          {report.status === 'generating' && '生成中'}
                          {report.status === 'failed' && '失败'}
                          {report.status === 'pending' && '等待中'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-2">
                {keywords.filter(k => k.is_active).length > 0 ? (
                  <>
                    <Button
                      variant="brand"
                      onClick={handleGenerateReport}
                      disabled={isGeneratingReport}
                      className="w-full sm:w-auto"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {isGeneratingReport ? '生成中...' : '生成报告'}
                    </Button>
                    {reports.length === 0 && (
                      <Button
                        variant="outline"
                        onClick={handleAddSampleReports}
                        disabled={isAddingSampleReports}
                        className="w-full sm:w-auto"
                      >
                        <Sparkles className="h-4 w-4 mr-2" />
                        {isAddingSampleReports ? '添加中...' : '添加示例报告'}
                      </Button>
                    )}
                    {reports.length > 0 && (
                      <Link href="/dashboard/reports" className="w-full sm:w-auto">
                        <Button variant="outline" className="w-full">
                          <BarChart3 className="h-4 w-4 mr-2" />
                          查看所有报告
                        </Button>
                      </Link>
                    )}
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    请先添加并启用关键词以生成报告
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Info Card */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>账户信息</CardTitle>
              <CardDescription>您的账户详细信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      用户ID
                    </label>
                    <p className="text-sm font-mono bg-muted p-2 rounded">
                      {user?.id}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      邮箱地址
                    </label>
                    <p className="text-sm">
                      {user?.email}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      姓名
                    </label>
                    <p className="text-sm">
                      {user?.name || '未设置'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      注册时间
                    </label>
                    <p className="text-sm">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString('zh-CN') : '未知'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* 添加关键词对话框 */}
      <AddKeywordDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={handleCreateKeyword}
        loading={isCreating}
      />
    </div>
  )
}
