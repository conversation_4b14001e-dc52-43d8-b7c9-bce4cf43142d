'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useKeywords } from '@/hooks/useKeywords'
import { KeywordService } from '@/lib/api/keywords'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AddKeywordDialog } from '@/components/keywords/AddKeywordDialog'
import { KeywordList } from '@/components/keywords/KeywordList'
import { TrendingUp, LogOut, User, Settings, Plus, BarChart3, Clock, Target, Sparkles } from 'lucide-react'
import type { CreateKeywordRequest } from '@/types/keyword'

export default function DashboardPage() {
  const { user, signOut, loading: authLoading } = useAuth()
  const {
    keywords,
    stats,
    loading: keywordsLoading,
    error: keywordsError,
    createKeyword,
    deleteKeyword,
    toggleKeywordStatus
  } = useKeywords()

  const [showAddDialog, setShowAddDialog] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [isAddingSample, setIsAddingSample] = useState(false)

  if (authLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <TrendingUp className="h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const handleCreateKeyword = async (data: CreateKeywordRequest) => {
    setIsCreating(true)
    try {
      await createKeyword(data)
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteKeyword = async (keywordId: string) => {
    if (confirm('确定要删除这个关键词吗？此操作无法撤销。')) {
      try {
        await deleteKeyword(keywordId)
      } catch (error) {
        console.error('删除关键词失败:', error)
      }
    }
  }

  const handleAddSampleData = async () => {
    if (!user?.id) return

    setIsAddingSample(true)
    try {
      await KeywordService.addSampleData(user.id)
      // 刷新关键词列表
      window.location.reload()
    } catch (error) {
      console.error('添加示例数据失败:', error)
    } finally {
      setIsAddingSample(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-brand-primary" />
              <h1 className="text-2xl font-bold text-foreground">报报</h1>
              <Badge variant="brand">Beta</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {user?.name || user?.email}
                </span>
              </div>
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
              <Button variant="outline" onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                登出
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-foreground mb-2">
              欢迎回来，{user?.name || '用户'}！ 👋
            </h2>
            <p className="text-muted-foreground text-lg">
              这里是您的个人仪表板，管理关键词和查看趋势报告
            </p>
          </div>
          <div className="flex gap-2">
            {keywords.length === 0 && (
              <Button
                variant="outline"
                onClick={handleAddSampleData}
                disabled={isAddingSample}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                {isAddingSample ? '添加中...' : '添加示例数据'}
              </Button>
            )}
            <Button variant="brand" onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加关键词
            </Button>
          </div>
        </div>

        {/* 错误提示 */}
        {keywordsError && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{keywordsError}</p>
            </CardContent>
          </Card>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">总关键词</p>
                  <p className="text-2xl font-bold">{stats?.total_keywords || 0}</p>
                </div>
                <Target className="h-8 w-8 text-brand-primary" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">活跃关键词</p>
                  <p className="text-2xl font-bold">{stats?.active_keywords || 0}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-success" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">生成报告</p>
                  <p className="text-2xl font-bold">{stats?.reports_generated || 0}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-info" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">最后更新</p>
                  <p className="text-sm font-medium">
                    {stats?.last_update
                      ? new Date(stats.last_update).toLocaleDateString('zh-CN')
                      : '暂无'
                    }
                  </p>
                </div>
                <Clock className="h-8 w-8 text-warning" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 关键词列表 */}
        <Card>
          <CardHeader>
            <CardTitle>我的关键词</CardTitle>
            <CardDescription>
              管理您关注的关键词，系统将为活跃的关键词生成趋势分析报告
            </CardDescription>
          </CardHeader>
          <CardContent>
            <KeywordList
              keywords={keywords}
              loading={keywordsLoading}
              onDelete={handleDeleteKeyword}
              onToggleStatus={toggleKeywordStatus}
            />
          </CardContent>
        </Card>

        {/* User Info Card */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>账户信息</CardTitle>
              <CardDescription>您的账户详细信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      用户ID
                    </label>
                    <p className="text-sm font-mono bg-muted p-2 rounded">
                      {user?.id}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      邮箱地址
                    </label>
                    <p className="text-sm">
                      {user?.email}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      姓名
                    </label>
                    <p className="text-sm">
                      {user?.name || '未设置'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      注册时间
                    </label>
                    <p className="text-sm">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString('zh-CN') : '未知'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* 添加关键词对话框 */}
      <AddKeywordDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={handleCreateKeyword}
        loading={isCreating}
      />
    </div>
  )
}
