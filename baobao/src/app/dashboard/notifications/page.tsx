'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useKeywords } from '@/hooks/useKeywords'
import { useNotifications } from '@/hooks/useNotifications'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { NotificationSettingsForm } from '@/components/notifications/NotificationSettingsForm'
import { NotificationPreview } from '@/components/notifications/NotificationPreview'
import { 
  TrendingUp, 
  ArrowLeft, 
  Bell, 
  Plus,
  Settings as SettingsIcon,
  AlertCircle
} from 'lucide-react'
import type { CreateNotificationSettingsRequest, UpdateNotificationSettingsRequest } from '@/types/notification'

export default function NotificationsPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const { keywords, loading: keywordsLoading } = useKeywords()
  const { 
    settings, 
    preview, 
    loading: notificationsLoading, 
    error,
    hasSettings,
    createSettings,
    updateSettings,
    deleteSettings,
    toggleSettings
  } = useNotifications()

  const [isCreating, setIsCreating] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  if (authLoading || keywordsLoading || notificationsLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <TrendingUp className="h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  const handleCreateSettings = async (settingsData: CreateNotificationSettingsRequest) => {
    setIsCreating(true)
    try {
      await createSettings(settingsData)
    } finally {
      setIsCreating(false)
    }
  }

  const handleUpdateSettings = async (updates: UpdateNotificationSettingsRequest) => {
    setIsUpdating(true)
    try {
      await updateSettings(updates)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDeleteSettings = async () => {
    if (confirm('确定要删除推送设置吗？此操作无法撤销。')) {
      try {
        await deleteSettings()
      } catch (error) {
        console.error('删除设置失败:', error)
      }
    }
  }

  const handleToggleSettings = async (enabled: boolean) => {
    try {
      await toggleSettings(enabled)
    } catch (error) {
      console.error('切换设置失败:', error)
    }
  }

  const activeKeywords = keywords.filter(k => k.is_active)

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.back()}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div className="flex items-center space-x-2">
                <Bell className="h-6 w-6 text-brand-primary" />
                <h1 className="text-2xl font-bold text-foreground">推送设置</h1>
                <Badge variant="brand">Beta</Badge>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* 页面标题和描述 */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            配置推送通知 📬
          </h2>
          <p className="text-muted-foreground text-lg">
            设置报告的推送时间和频率，让您及时获得最新的趋势分析
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <Card className="mb-6 border-destructive">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-destructive" />
                <p className="text-destructive text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 关键词检查 */}
        {activeKeywords.length === 0 && (
          <Card className="mb-6 border-warning">
            <CardContent className="p-6">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-warning mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">没有活跃的关键词</h3>
                <p className="text-muted-foreground mb-4">
                  您需要先添加并启用关键词，才能设置推送通知
                </p>
                <Button
                  variant="brand"
                  onClick={() => router.push('/dashboard')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  添加关键词
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 设置表单 */}
          <div className="lg:col-span-2">
            {activeKeywords.length > 0 && (
              <NotificationSettingsForm
                keywords={keywords}
                initialSettings={settings}
                onSave={hasSettings ? handleUpdateSettings : handleCreateSettings}
                onDelete={hasSettings ? handleDeleteSettings : undefined}
                onToggle={hasSettings ? handleToggleSettings : undefined}
                loading={isCreating || isUpdating}
                isEditing={hasSettings}
              />
            )}
          </div>

          {/* 预览面板 */}
          <div className="space-y-6">
            {settings && preview && (
              <NotificationPreview
                settings={settings}
                preview={preview}
                loading={notificationsLoading}
              />
            )}

            {/* 帮助信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  使用说明
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <h4 className="font-medium mb-1">📅 推送频率</h4>
                  <p className="text-muted-foreground">
                    选择每日、每周或每月推送，系统将自动生成趋势分析报告
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">⏰ 推送时间</h4>
                  <p className="text-muted-foreground">
                    建议选择您常用设备的活跃时间，确保及时查看报告
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">🎯 关键词选择</h4>
                  <p className="text-muted-foreground">
                    可以选择特定关键词，或包含所有活跃关键词
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">📧 推送方式</h4>
                  <p className="text-muted-foreground">
                    支持邮件和浏览器推送，建议至少启用一种方式
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 快速操作 */}
            {!hasSettings && activeKeywords.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>快速设置</CardTitle>
                  <CardDescription>使用预设配置快速开始</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleCreateSettings({
                      frequency: 'daily',
                      time: '09:00',
                      email_enabled: true,
                      push_enabled: false
                    })}
                    disabled={isCreating}
                  >
                    📅 每日早上9点推送
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => handleCreateSettings({
                      frequency: 'weekly',
                      time: '10:00',
                      week_days: ['monday'],
                      email_enabled: true,
                      push_enabled: false
                    })}
                    disabled={isCreating}
                  >
                    📊 每周一上午10点推送
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
