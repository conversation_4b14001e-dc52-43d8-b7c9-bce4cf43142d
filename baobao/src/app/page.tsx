import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Bell, Search, Plus, LogIn, UserPlus } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-brand-primary" />
              <h1 className="text-2xl font-bold text-foreground">报报</h1>
              <Badge variant="brand" className="ml-2">Beta</Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost">
                  <LogIn className="h-4 w-4 mr-2" />
                  登录
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="brand">
                  <UserPlus className="h-4 w-4 mr-2" />
                  注册
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            欢迎使用报报 📊
          </h2>
          <p className="text-muted-foreground text-lg">
            智能趋势分析，让您掌握最新动态
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索关键词或报告..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Design System Demo */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Buttons Demo */}
          <Card>
            <CardHeader>
              <CardTitle>按钮组件</CardTitle>
              <CardDescription>不同样式的按钮展示</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button variant="default">默认按钮</Button>
                <Button variant="brand">品牌按钮</Button>
                <Button variant="secondary">次要按钮</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline">轮廓按钮</Button>
                <Button variant="ghost">幽灵按钮</Button>
                <Button variant="link">链接按钮</Button>
              </div>
            </CardContent>
          </Card>

          {/* Badges Demo */}
          <Card>
            <CardHeader>
              <CardTitle>标签组件</CardTitle>
              <CardDescription>状态和分类标签</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="default">默认</Badge>
                <Badge variant="brand">品牌</Badge>
                <Badge variant="secondary">次要</Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge variant="success">成功</Badge>
                <Badge variant="warning">警告</Badge>
                <Badge variant="info">信息</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Colors Demo */}
          <Card>
            <CardHeader>
              <CardTitle>颜色系统</CardTitle>
              <CardDescription>品牌色彩展示</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-2">
                <div className="h-12 bg-brand-primary rounded flex items-center justify-center text-white text-xs">
                  Primary
                </div>
                <div className="h-12 bg-brand-secondary rounded flex items-center justify-center text-white text-xs">
                  Secondary
                </div>
                <div className="h-12 bg-brand-accent rounded flex items-center justify-center text-white text-xs">
                  Accent
                </div>
              </div>
              <div className="grid grid-cols-3 gap-2">
                <div className="h-12 bg-success rounded flex items-center justify-center text-white text-xs">
                  Success
                </div>
                <div className="h-12 bg-warning rounded flex items-center justify-center text-white text-xs">
                  Warning
                </div>
                <div className="h-12 bg-info rounded flex items-center justify-center text-white text-xs">
                  Info
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Feature Preview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>关键词管理</CardTitle>
              <CardDescription>添加和管理您关注的关键词</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input placeholder="输入关键词，如：人工智能大模型" />
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">人工智能大模型</Badge>
                  <Badge variant="outline">区块链技术</Badge>
                  <Badge variant="outline">新能源汽车</Badge>
                </div>
                <Button className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  添加关键词
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>推送设置</CardTitle>
              <CardDescription>配置报告推送时间和频率</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="justify-start">
                    每日推送
                  </Button>
                  <Button variant="outline" className="justify-start">
                    每周推送
                  </Button>
                </div>
                <Input type="time" defaultValue="09:00" />
                <Button variant="brand" className="w-full">
                  保存设置
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
