{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tMHBKZIB7Y142mtKgCV5JjBknafhIyMd2byqEr7ABMA=", "__NEXT_PREVIEW_MODE_ID": "a3669c8d69d871b6c15a49e6a1855b7c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "000cb30ad4263bd865a62bfd86a1d8522dd22ed4fa2dd8ae858dbaf9a203c5dc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "04fd30945e94cdb1848599a691f7b91db226474379c86c7866b6be3e95f9084f"}}}, "instrumentation": null, "functions": {}}