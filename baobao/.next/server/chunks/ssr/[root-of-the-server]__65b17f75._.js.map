{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport type { AuthContextType, User, LoginCredentials, RegisterCredentials } from '@/types/auth'\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const supabase = createClient()\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      if (session?.user) {\n        setUser({\n          id: session.user.id,\n          email: session.user.email!,\n          name: session.user.user_metadata?.name,\n          avatar_url: session.user.user_metadata?.avatar_url,\n          created_at: session.user.created_at,\n          updated_at: session.user.updated_at || session.user.created_at,\n        })\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (session?.user) {\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            name: session.user.user_metadata?.name,\n            avatar_url: session.user.user_metadata?.avatar_url,\n            created_at: session.user.created_at,\n            updated_at: session.user.updated_at || session.user.created_at,\n          })\n        } else {\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase.auth])\n\n  const signIn = async (credentials: LoginCredentials) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { error } = await supabase.auth.signInWithPassword(credentials)\n      if (error) throw error\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '登录失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (credentials: RegisterCredentials) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { error } = await supabase.auth.signUp({\n        email: credentials.email,\n        password: credentials.password,\n        options: {\n          data: {\n            name: credentials.name,\n          },\n        },\n      })\n      if (error) throw error\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '注册失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '登出失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    setLoading(true)\n    setError(null)\n    \n    try {\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n      if (error) throw error\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '重置密码失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAC5D,IAAI,SAAS,MAAM;gBACjB,QAAQ;oBACN,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,MAAM,QAAQ,IAAI,CAAC,aAAa,EAAE;oBAClC,YAAY,QAAQ,IAAI,CAAC,aAAa,EAAE;oBACxC,YAAY,QAAQ,IAAI,CAAC,UAAU;oBACnC,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,QAAQ,IAAI,CAAC,UAAU;gBAChE;YACF;YACA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,SAAS,MAAM;gBACjB,QAAQ;oBACN,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,MAAM,QAAQ,IAAI,CAAC,aAAa,EAAE;oBAClC,YAAY,QAAQ,IAAI,CAAC,aAAa,EAAE;oBACxC,YAAY,QAAQ,IAAI,CAAC,UAAU;oBACnC,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,QAAQ,IAAI,CAAC,UAAU;gBAChE;YACF,OAAO;gBACL,QAAQ;YACV;YACA,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,SAAS,OAAO;QACpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACzD,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO;QACpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,SAAS;oBACP,MAAM;wBACJ,MAAM,YAAY,IAAI;oBACxB;gBACF;YACF;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YACA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}