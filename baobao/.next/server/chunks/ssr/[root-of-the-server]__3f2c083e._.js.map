{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport type { AuthContextType, User, LoginCredentials, RegisterCredentials } from '@/types/auth'\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\n// 模拟用户存储\nconst STORAGE_KEY = 'baobao_user'\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    // 检查本地存储中的用户信息\n    const checkStoredUser = () => {\n      try {\n        const storedUser = localStorage.getItem(STORAGE_KEY)\n        if (storedUser) {\n          setUser(JSON.parse(storedUser))\n        }\n      } catch (error) {\n        console.error('读取用户信息失败:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkStoredUser()\n  }, [])\n\n  const signIn = async (credentials: LoginCredentials) => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // 简单的模拟验证\n      if (credentials.email === '<EMAIL>' && credentials.password === 'demo123456') {\n        const mockUser: User = {\n          id: 'demo-user-id',\n          email: credentials.email,\n          name: '演示用户',\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        }\n\n        setUser(mockUser)\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(mockUser))\n      } else {\n        throw new Error('邮箱或密码错误')\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '登录失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (credentials: RegisterCredentials) => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // 简单的模拟注册\n      if (credentials.email && credentials.password) {\n        // 在真实应用中，这里会创建用户账户\n        // 现在我们只是模拟成功\n        console.log('模拟注册成功:', credentials.email)\n      } else {\n        throw new Error('请填写完整信息')\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '注册失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 500))\n\n      setUser(null)\n      localStorage.removeItem(STORAGE_KEY)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '登出失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    setLoading(true)\n    setError(null)\n\n    try {\n      // 模拟网络延迟\n      await new Promise(resolve => setTimeout(resolve, 1000))\n\n      // 模拟发送重置邮件\n      console.log('模拟发送重置密码邮件到:', email)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '重置密码失败')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    error,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS;AACT,MAAM,cAAc;AAEb,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,QAAQ,KAAK,KAAK,CAAC;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;YAC7B,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO;QACpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,UAAU;YACV,IAAI,YAAY,KAAK,KAAK,qBAAqB,YAAY,QAAQ,KAAK,cAAc;gBACpF,MAAM,WAAiB;oBACrB,IAAI;oBACJ,OAAO,YAAY,KAAK;oBACxB,MAAM;oBACN,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBAEA,QAAQ;gBACR,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;YACnD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO;QACpB,WAAW;QACX,SAAS;QAET,IAAI;YACF,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,UAAU;YACV,IAAI,YAAY,KAAK,IAAI,YAAY,QAAQ,EAAE;gBAC7C,mBAAmB;gBACnB,aAAa;gBACb,QAAQ,GAAG,CAAC,WAAW,YAAY,KAAK;YAC1C,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,WAAW;QACX,SAAS;QAET,IAAI;YACF,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,QAAQ;YACR,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,SAAS;QAET,IAAI;YACF,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,WAAW;YACX,QAAQ,GAAG,CAAC,gBAAgB;QAC9B,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}