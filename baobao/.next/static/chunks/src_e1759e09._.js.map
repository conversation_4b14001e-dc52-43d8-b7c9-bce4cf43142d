{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/lib/api/keywords-mock.ts"], "sourcesContent": ["import type { Keyword, CreateKeywordRequest, UpdateKeywordRequest } from '@/types/keyword'\n\n// 模拟数据存储 - 在实际应用中这将是 Supabase 数据库\nconst STORAGE_KEY = 'baobao_keywords'\n\n// 生成UUID的简单函数\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\n// 获取存储的关键词\nfunction getStoredKeywords(): Keyword[] {\n  if (typeof window === 'undefined') return []\n  \n  try {\n    const stored = localStorage.getItem(STORAGE_KEY)\n    return stored ? JSON.parse(stored) : []\n  } catch {\n    return []\n  }\n}\n\n// 保存关键词到存储\nfunction saveKeywords(keywords: Keyword[]): void {\n  if (typeof window === 'undefined') return\n  \n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(keywords))\n  } catch (error) {\n    console.error('保存关键词失败:', error)\n  }\n}\n\nexport class MockKeywordService {\n  static async getKeywords(userId: string): Promise<Keyword[]> {\n    // 模拟网络延迟\n    await new Promise(resolve => setTimeout(resolve, 300))\n    \n    const allKeywords = getStoredKeywords()\n    return allKeywords.filter(keyword => keyword.user_id === userId)\n  }\n\n  static async createKeyword(userId: string, keywordData: CreateKeywordRequest): Promise<Keyword> {\n    // 模拟网络延迟\n    await new Promise(resolve => setTimeout(resolve, 500))\n    \n    const now = new Date().toISOString()\n    const newKeyword: Keyword = {\n      id: generateId(),\n      user_id: userId,\n      name: keywordData.name,\n      description: keywordData.description,\n      category: keywordData.category,\n      is_active: true,\n      created_at: now,\n      updated_at: now,\n    }\n\n    const allKeywords = getStoredKeywords()\n    \n    // 检查是否已存在相同名称的关键词\n    const existingKeyword = allKeywords.find(\n      k => k.user_id === userId && k.name.toLowerCase() === keywordData.name.toLowerCase()\n    )\n    \n    if (existingKeyword) {\n      throw new Error('该关键词已存在')\n    }\n\n    allKeywords.push(newKeyword)\n    saveKeywords(allKeywords)\n    \n    return newKeyword\n  }\n\n  static async updateKeyword(keywordId: string, updates: UpdateKeywordRequest): Promise<Keyword> {\n    // 模拟网络延迟\n    await new Promise(resolve => setTimeout(resolve, 400))\n    \n    const allKeywords = getStoredKeywords()\n    const keywordIndex = allKeywords.findIndex(k => k.id === keywordId)\n    \n    if (keywordIndex === -1) {\n      throw new Error('关键词不存在')\n    }\n\n    const updatedKeyword = {\n      ...allKeywords[keywordIndex],\n      ...updates,\n      updated_at: new Date().toISOString(),\n    }\n\n    allKeywords[keywordIndex] = updatedKeyword\n    saveKeywords(allKeywords)\n    \n    return updatedKeyword\n  }\n\n  static async deleteKeyword(keywordId: string): Promise<void> {\n    // 模拟网络延迟\n    await new Promise(resolve => setTimeout(resolve, 300))\n    \n    const allKeywords = getStoredKeywords()\n    const filteredKeywords = allKeywords.filter(k => k.id !== keywordId)\n    \n    if (filteredKeywords.length === allKeywords.length) {\n      throw new Error('关键词不存在')\n    }\n    \n    saveKeywords(filteredKeywords)\n  }\n\n  static async toggleKeywordStatus(keywordId: string, isActive: boolean): Promise<Keyword> {\n    return this.updateKeyword(keywordId, { is_active: isActive })\n  }\n\n  static async getKeywordStats(userId: string) {\n    // 模拟网络延迟\n    await new Promise(resolve => setTimeout(resolve, 200))\n    \n    const keywords = await this.getKeywords(userId)\n    \n    const totalKeywords = keywords.length\n    const activeKeywords = keywords.filter(k => k.is_active).length\n    const reportsGenerated = keywords.filter(k => k.last_report_generated_at).length\n    const lastUpdate = keywords.length > 0 \n      ? Math.max(...keywords.map(k => new Date(k.created_at).getTime()))\n      : Date.now()\n\n    return {\n      total_keywords: totalKeywords,\n      active_keywords: activeKeywords,\n      reports_generated: reportsGenerated,\n      last_update: new Date(lastUpdate).toISOString(),\n    }\n  }\n\n  // 添加一些示例数据的方法\n  static async addSampleData(userId: string): Promise<void> {\n    const sampleKeywords = [\n      {\n        name: '人工智能大模型',\n        description: '关注AI大模型的最新发展趋势和技术突破',\n        category: '科技',\n      },\n      {\n        name: '新能源汽车',\n        description: '电动汽车市场动态和技术创新',\n        category: '汽车',\n      },\n      {\n        name: '区块链技术',\n        description: '区块链在各行业的应用和发展',\n        category: '科技',\n      },\n    ]\n\n    for (const keyword of sampleKeywords) {\n      try {\n        await this.createKeyword(userId, keyword)\n      } catch (error) {\n        // 忽略重复关键词错误\n        console.log(`跳过已存在的关键词: ${keyword.name}`)\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAEA,kCAAkC;AAClC,MAAM,cAAc;AAEpB,cAAc;AACd,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAEA,WAAW;AACX,SAAS;IACP,uCAAmC;;IAAQ;IAE3C,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;IACzC,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,aAAa,QAAmB;IACvC,uCAAmC;;IAAK;IAExC,IAAI;QACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;IAC5B;AACF;AAEO,MAAM;IACX,aAAa,YAAY,MAAc,EAAsB;QAC3D,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,cAAc;QACpB,OAAO,YAAY,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IAC3D;IAEA,aAAa,cAAc,MAAc,EAAE,WAAiC,EAAoB;QAC9F,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,MAAM,IAAI,OAAO,WAAW;QAClC,MAAM,aAAsB;YAC1B,IAAI;YACJ,SAAS;YACT,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,UAAU,YAAY,QAAQ;YAC9B,WAAW;YACX,YAAY;YACZ,YAAY;QACd;QAEA,MAAM,cAAc;QAEpB,kBAAkB;QAClB,MAAM,kBAAkB,YAAY,IAAI,CACtC,CAAA,IAAK,EAAE,OAAO,KAAK,UAAU,EAAE,IAAI,CAAC,WAAW,OAAO,YAAY,IAAI,CAAC,WAAW;QAGpF,IAAI,iBAAiB;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,YAAY,IAAI,CAAC;QACjB,aAAa;QAEb,OAAO;IACT;IAEA,aAAa,cAAc,SAAiB,EAAE,OAA6B,EAAoB;QAC7F,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,cAAc;QACpB,MAAM,eAAe,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAEzD,IAAI,iBAAiB,CAAC,GAAG;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB;YACrB,GAAG,WAAW,CAAC,aAAa;YAC5B,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,WAAW,CAAC,aAAa,GAAG;QAC5B,aAAa;QAEb,OAAO;IACT;IAEA,aAAa,cAAc,SAAiB,EAAiB;QAC3D,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,cAAc;QACpB,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE1D,IAAI,iBAAiB,MAAM,KAAK,YAAY,MAAM,EAAE;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,aAAa;IACf;IAEA,aAAa,oBAAoB,SAAiB,EAAE,QAAiB,EAAoB;QACvF,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW;YAAE,WAAW;QAAS;IAC7D;IAEA,aAAa,gBAAgB,MAAc,EAAE;QAC3C,SAAS;QACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAExC,MAAM,gBAAgB,SAAS,MAAM;QACrC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAC/D,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,wBAAwB,EAAE,MAAM;QAChF,MAAM,aAAa,SAAS,MAAM,GAAG,IACjC,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,OAC5D,KAAK,GAAG;QAEZ,OAAO;YACL,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,aAAa,IAAI,KAAK,YAAY,WAAW;QAC/C;IACF;IAEA,cAAc;IACd,aAAa,cAAc,MAAc,EAAiB;QACxD,MAAM,iBAAiB;YACrB;gBACE,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;SACD;QAED,KAAK,MAAM,WAAW,eAAgB;YACpC,IAAI;gBACF,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ;YACnC,EAAE,OAAO,OAAO;gBACd,YAAY;gBACZ,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,IAAI,EAAE;YAC1C;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/lib/api/keywords.ts"], "sourcesContent": ["import { MockKeywordService } from './keywords-mock'\nimport type { Keyword, CreateKeywordRequest, UpdateKeywordRequest } from '@/types/keyword'\n\n// 在开发环境中使用模拟服务，生产环境中可以切换到真实的 Supabase 服务\nconst USE_MOCK = true // 设置为 false 来使用真实的 Supabase 服务\n\nexport class KeywordService {\n  static async getKeywords(userId: string): Promise<Keyword[]> {\n    if (USE_MOCK) {\n      return MockKeywordService.getKeywords(userId)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  static async createKeyword(userId: string, keywordData: CreateKeywordRequest): Promise<Keyword> {\n    if (USE_MOCK) {\n      return MockKeywordService.createKeyword(userId, keywordData)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  static async updateKeyword(keywordId: string, updates: UpdateKeywordRequest): Promise<Keyword> {\n    if (USE_MOCK) {\n      return MockKeywordService.updateKeyword(keywordId, updates)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  static async deleteKeyword(keywordId: string): Promise<void> {\n    if (USE_MOCK) {\n      return MockKeywordService.deleteKeyword(keywordId)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  static async toggleKeywordStatus(keywordId: string, isActive: boolean): Promise<Keyword> {\n    if (USE_MOCK) {\n      return MockKeywordService.toggleKeywordStatus(keywordId, isActive)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  static async getKeywordStats(userId: string) {\n    if (USE_MOCK) {\n      return MockKeywordService.getKeywordStats(userId)\n    }\n\n    // 真实的 Supabase 实现将在这里\n    throw new Error('Supabase 服务尚未配置')\n  }\n\n  // 添加示例数据的便捷方法\n  static async addSampleData(userId: string): Promise<void> {\n    if (USE_MOCK) {\n      return MockKeywordService.addSampleData(userId)\n    }\n\n    throw new Error('示例数据功能仅在模拟模式下可用')\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,yCAAyC;AACzC,MAAM,WAAW,KAAK,+BAA+B;;AAE9C,MAAM;IACX,aAAa,YAAY,MAAc,EAAsB;QAC3D,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;QACxC;;IAIF;IAEA,aAAa,cAAc,MAAc,EAAE,WAAiC,EAAoB;QAC9F,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,QAAQ;QAClD;;IAIF;IAEA,aAAa,cAAc,SAAiB,EAAE,OAA6B,EAAoB;QAC7F,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC,WAAW;QACrD;;IAIF;IAEA,aAAa,cAAc,SAAiB,EAAiB;QAC3D,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;QAC1C;;IAIF;IAEA,aAAa,oBAAoB,SAAiB,EAAE,QAAiB,EAAoB;QACvF,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC,WAAW;QAC3D;;IAIF;IAEA,aAAa,gBAAgB,MAAc,EAAE;QAC3C,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;QAC5C;;IAIF;IAEA,cAAc;IACd,aAAa,cAAc,MAAc,EAAiB;QACxD,wCAAc;YACZ,OAAO,wIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;QAC1C;;IAGF;AACF", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/hooks/useKeywords.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { KeywordService } from '@/lib/api/keywords'\nimport type { Keyword, CreateKeywordRequest, UpdateKeywordRequest, KeywordStats } from '@/types/keyword'\n\nexport function useKeywords() {\n  const { user } = useAuth()\n  const [keywords, setKeywords] = useState<Keyword[]>([])\n  const [stats, setStats] = useState<KeywordStats | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchKeywords = useCallback(async () => {\n    if (!user?.id) return\n\n    try {\n      setLoading(true)\n      setError(null)\n      const data = await KeywordService.getKeywords(user.id)\n      setKeywords(data)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '获取关键词失败')\n    } finally {\n      setLoading(false)\n    }\n  }, [user?.id])\n\n  const fetchStats = useCallback(async () => {\n    if (!user?.id) return\n\n    try {\n      const statsData = await KeywordService.getKeywordStats(user.id)\n      setStats(statsData)\n    } catch (err) {\n      console.error('获取统计数据失败:', err)\n    }\n  }, [user?.id])\n\n  const createKeyword = async (keywordData: CreateKeywordRequest) => {\n    if (!user?.id) throw new Error('用户未登录')\n\n    try {\n      const newKeyword = await KeywordService.createKeyword(user.id, keywordData)\n      setKeywords(prev => [newKeyword, ...prev])\n      await fetchStats() // 更新统计数据\n      return newKeyword\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : '创建关键词失败'\n      setError(errorMessage)\n      throw err\n    }\n  }\n\n  const updateKeyword = async (keywordId: string, updates: UpdateKeywordRequest) => {\n    try {\n      const updatedKeyword = await KeywordService.updateKeyword(keywordId, updates)\n      setKeywords(prev => \n        prev.map(keyword => \n          keyword.id === keywordId ? updatedKeyword : keyword\n        )\n      )\n      await fetchStats() // 更新统计数据\n      return updatedKeyword\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : '更新关键词失败'\n      setError(errorMessage)\n      throw err\n    }\n  }\n\n  const deleteKeyword = async (keywordId: string) => {\n    try {\n      await KeywordService.deleteKeyword(keywordId)\n      setKeywords(prev => prev.filter(keyword => keyword.id !== keywordId))\n      await fetchStats() // 更新统计数据\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : '删除关键词失败'\n      setError(errorMessage)\n      throw err\n    }\n  }\n\n  const toggleKeywordStatus = async (keywordId: string, isActive: boolean) => {\n    try {\n      const updatedKeyword = await KeywordService.toggleKeywordStatus(keywordId, isActive)\n      setKeywords(prev => \n        prev.map(keyword => \n          keyword.id === keywordId ? updatedKeyword : keyword\n        )\n      )\n      await fetchStats() // 更新统计数据\n      return updatedKeyword\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : '更新关键词状态失败'\n      setError(errorMessage)\n      throw err\n    }\n  }\n\n  useEffect(() => {\n    if (user?.id) {\n      fetchKeywords()\n      fetchStats()\n    }\n  }, [user?.id, fetchKeywords, fetchStats])\n\n  return {\n    keywords,\n    stats,\n    loading,\n    error,\n    createKeyword,\n    updateKeyword,\n    deleteKeyword,\n    toggleKeywordStatus,\n    refetch: fetchKeywords,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,IAAI,CAAC,MAAM,IAAI;YAEf,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,OAAO,MAAM,gIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,KAAK,EAAE;gBACrD,YAAY;YACd,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;iDAAG;QAAC,MAAM;KAAG;IAEb,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC7B,IAAI,CAAC,MAAM,IAAI;YAEf,IAAI;gBACF,MAAM,YAAY,MAAM,gIAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC9D,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;8CAAG;QAAC,MAAM;KAAG;IAEb,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM;QAE/B,IAAI;YACF,MAAM,aAAa,MAAM,gIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;YAC/D,YAAY,CAAA,OAAQ;oBAAC;uBAAe;iBAAK;YACzC,MAAM,aAAa,SAAS;;YAC5B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO,WAAmB;QAC9C,IAAI;YACF,MAAM,iBAAiB,MAAM,gIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,WAAW;YACrE,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,YAAY,iBAAiB;YAGhD,MAAM,aAAa,SAAS;;YAC5B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,gIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;YACnC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;YAC1D,MAAM,aAAa,SAAS;;QAC9B,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,OAAO,WAAmB;QACpD,IAAI;YACF,MAAM,iBAAiB,MAAM,gIAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,WAAW;YAC3E,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,YAAY,iBAAiB;YAGhD,MAAM,aAAa,SAAS;;YAC5B,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,MAAM,IAAI;gBACZ;gBACA;YACF;QACF;gCAAG;QAAC,MAAM;QAAI;QAAe;KAAW;IAExC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;GAhHgB;;QACG,kIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        brand: \"bg-brand-primary text-white hover:bg-brand-primary/90\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-success text-white hover:bg-success/80\",\n        warning:\n          \"border-transparent bg-warning text-white hover:bg-warning/80\",\n        info:\n          \"border-transparent bg-info text-white hover:bg-info/80\",\n        brand:\n          \"border-transparent bg-brand-primary text-white hover:bg-brand-primary/80\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,OACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/keywords/AddKeywordDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Plus, X, Tag, FileText, Folder } from 'lucide-react'\nimport type { CreateKeywordRequest } from '@/types/keyword'\n\ninterface AddKeywordDialogProps {\n  isOpen: boolean\n  onClose: () => void\n  onSubmit: (data: CreateKeywordRequest) => Promise<void>\n  loading?: boolean\n}\n\nconst SUGGESTED_CATEGORIES = [\n  '科技', '商业', '健康', '教育', '娱乐', '体育', '政治', '环境', '金融', '社会'\n]\n\nconst KEYWORD_EXAMPLES = [\n  '人工智能大模型',\n  '新能源汽车',\n  '区块链技术',\n  '元宇宙',\n  '量子计算',\n  '生物技术',\n]\n\nexport function AddKeywordDialog({ isOpen, onClose, onSubmit, loading = false }: AddKeywordDialogProps) {\n  const [formData, setFormData] = useState<CreateKeywordRequest>({\n    name: '',\n    description: '',\n    category: '',\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  if (!isOpen) return null\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    // 验证表单\n    const newErrors: Record<string, string> = {}\n    if (!formData.name.trim()) {\n      newErrors.name = '关键词名称不能为空'\n    }\n    if (formData.name.length > 50) {\n      newErrors.name = '关键词名称不能超过50个字符'\n    }\n    if (formData.description && formData.description.length > 200) {\n      newErrors.description = '描述不能超过200个字符'\n    }\n\n    setErrors(newErrors)\n    \n    if (Object.keys(newErrors).length > 0) {\n      return\n    }\n\n    try {\n      await onSubmit(formData)\n      // 重置表单\n      setFormData({ name: '', description: '', category: '' })\n      setErrors({})\n      onClose()\n    } catch (error) {\n      console.error('提交失败:', error)\n    }\n  }\n\n  const handleClose = () => {\n    setFormData({ name: '', description: '', category: '' })\n    setErrors({})\n    onClose()\n  }\n\n  const selectExample = (example: string) => {\n    setFormData(prev => ({ ...prev, name: example }))\n  }\n\n  const selectCategory = (category: string) => {\n    setFormData(prev => ({ ...prev, category }))\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\">\n      <Card className=\"w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Plus className=\"h-5 w-5 text-brand-primary\" />\n                添加关键词\n              </CardTitle>\n              <CardDescription>\n                添加您想要跟踪的关键词，系统将为您生成相关的趋势分析报告\n              </CardDescription>\n            </div>\n            <Button variant=\"ghost\" size=\"icon\" onClick={handleClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* 关键词名称 */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"name\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Tag className=\"h-4 w-4\" />\n                关键词名称 *\n              </label>\n              <Input\n                id=\"name\"\n                placeholder=\"例如：人工智能大模型\"\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                className={errors.name ? 'border-destructive' : ''}\n              />\n              {errors.name && (\n                <p className=\"text-sm text-destructive\">{errors.name}</p>\n              )}\n            </div>\n\n            {/* 示例关键词 */}\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">快速选择示例</label>\n              <div className=\"flex flex-wrap gap-2\">\n                {KEYWORD_EXAMPLES.map((example) => (\n                  <Button\n                    key={example}\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => selectExample(example)}\n                    className=\"text-xs\"\n                  >\n                    {example}\n                  </Button>\n                ))}\n              </div>\n            </div>\n\n            {/* 描述 */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"description\" className=\"text-sm font-medium flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                描述 (可选)\n              </label>\n              <textarea\n                id=\"description\"\n                placeholder=\"简要描述这个关键词的含义或您关注的方面...\"\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${errors.description ? 'border-destructive' : ''}`}\n                rows={3}\n              />\n              {errors.description && (\n                <p className=\"text-sm text-destructive\">{errors.description}</p>\n              )}\n            </div>\n\n            {/* 分类 */}\n            <div className=\"space-y-2\">\n              <label htmlFor=\"category\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Folder className=\"h-4 w-4\" />\n                分类 (可选)\n              </label>\n              <Input\n                id=\"category\"\n                placeholder=\"例如：科技、商业、健康等\"\n                value={formData.category}\n                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n              />\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {SUGGESTED_CATEGORIES.map((category) => (\n                  <Badge\n                    key={category}\n                    variant=\"outline\"\n                    className=\"cursor-pointer hover:bg-brand-primary hover:text-white transition-colors\"\n                    onClick={() => selectCategory(category)}\n                  >\n                    {category}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n\n            {/* 提交按钮 */}\n            <div className=\"flex justify-end space-x-2 pt-4\">\n              <Button type=\"button\" variant=\"outline\" onClick={handleClose}>\n                取消\n              </Button>\n              <Button type=\"submit\" variant=\"brand\" disabled={loading}>\n                {loading ? '添加中...' : '添加关键词'}\n              </Button>\n            </div>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAiBA,MAAM,uBAAuB;IAC3B;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CACvD;AAED,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAyB;;IACpG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;QAC7D,MAAM;QACN,aAAa;QACb,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,OAAO;QACP,MAAM,YAAoC,CAAC;QAC3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QACA,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI;YAC7B,UAAU,IAAI,GAAG;QACnB;QACA,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK;YAC7D,UAAU,WAAW,GAAG;QAC1B;QAEA,UAAU;QAEV,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC;QACF;QAEA,IAAI;YACF,MAAM,SAAS;YACf,OAAO;YACP,YAAY;gBAAE,MAAM;gBAAI,aAAa;gBAAI,UAAU;YAAG;YACtD,UAAU,CAAC;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YAAE,MAAM;YAAI,aAAa;YAAI,UAAU;QAAG;QACtD,UAAU,CAAC;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAQ,CAAC;IACjD;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAS,CAAC;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;kDAGjD,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAC3C,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8BAInB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAO,WAAU;;0DAC9B,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG7B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,WAAW,OAAO,IAAI,GAAG,uBAAuB;;;;;;oCAEjD,OAAO,IAAI,kBACV,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,IAAI;;;;;;;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,qIAAA,CAAA,SAAM;gDAEL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,cAAc;gDAC7B,WAAU;0DAET;+CAPI;;;;;;;;;;;;;;;;0CAcb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAc,WAAU;;0DACrC,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,6LAAC;wCACC,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,WAAW,CAAC,qSAAqS,EAAE,OAAO,WAAW,GAAG,uBAAuB,IAAI;wCACnW,MAAM;;;;;;oCAEP,OAAO,WAAW,kBACjB,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,WAAW;;;;;;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;;0DAClC,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGhC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;;;;;;kDAE7E,6LAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG,CAAC,CAAC,yBACzB,6LAAC,oIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,eAAe;0DAE7B;+CALI;;;;;;;;;;;;;;;;0CAYb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAa;;;;;;kDAG9D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAQ,UAAU;kDAC7C,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GA7KgB;KAAA", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/components/keywords/KeywordList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { \n  MoreHorizontal, \n  Edit, \n  Trash2, \n  Power, \n  PowerOff, \n  Search,\n  Calendar,\n  Tag,\n  FileText\n} from 'lucide-react'\nimport { format } from 'date-fns'\nimport { zhCN } from 'date-fns/locale'\nimport type { Keyword } from '@/types/keyword'\n\ninterface KeywordListProps {\n  keywords: Keyword[]\n  loading?: boolean\n  onEdit?: (keyword: Keyword) => void\n  onDelete?: (keywordId: string) => void\n  onToggleStatus?: (keywordId: string, isActive: boolean) => void\n}\n\nexport function KeywordList({ \n  keywords, \n  loading = false, \n  onEdit, \n  onDelete, \n  onToggleStatus \n}: KeywordListProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\n  const [showInactive, setShowInactive] = useState(false)\n\n  // 获取所有分类\n  const categories = Array.from(new Set(keywords.map(k => k.category).filter(Boolean)))\n\n  // 过滤关键词\n  const filteredKeywords = keywords.filter(keyword => {\n    const matchesSearch = keyword.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         keyword.description?.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesCategory = !selectedCategory || keyword.category === selectedCategory\n    const matchesStatus = showInactive || keyword.is_active\n    \n    return matchesSearch && matchesCategory && matchesStatus\n  })\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <Card key={i} className=\"animate-pulse\">\n            <CardContent className=\"p-6\">\n              <div className=\"space-y-3\">\n                <div className=\"h-4 bg-muted rounded w-1/3\"></div>\n                <div className=\"h-3 bg-muted rounded w-2/3\"></div>\n                <div className=\"flex gap-2\">\n                  <div className=\"h-6 bg-muted rounded w-16\"></div>\n                  <div className=\"h-6 bg-muted rounded w-20\"></div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n    )\n  }\n\n  if (keywords.length === 0) {\n    return (\n      <Card>\n        <CardContent className=\"p-12 text-center\">\n          <Tag className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">还没有关键词</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            添加您的第一个关键词，开始生成趋势分析报告\n          </p>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 搜索和过滤 */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"relative flex-1\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"搜索关键词...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10\"\n          />\n        </div>\n        \n        <div className=\"flex gap-2\">\n          <select\n            value={selectedCategory}\n            onChange={(e) => setSelectedCategory(e.target.value)}\n            className=\"px-3 py-2 border border-input rounded-md bg-background text-sm\"\n          >\n            <option value=\"\">所有分类</option>\n            {categories.map(category => (\n              <option key={category} value={category}>{category}</option>\n            ))}\n          </select>\n          \n          <Button\n            variant={showInactive ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setShowInactive(!showInactive)}\n          >\n            {showInactive ? \"显示全部\" : \"包含已停用\"}\n          </Button>\n        </div>\n      </div>\n\n      {/* 关键词列表 */}\n      <div className=\"space-y-4\">\n        {filteredKeywords.map((keyword) => (\n          <Card key={keyword.id} className={!keyword.is_active ? 'opacity-60' : ''}>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 space-y-3\">\n                  {/* 标题和状态 */}\n                  <div className=\"flex items-center gap-3\">\n                    <h3 className=\"font-semibold text-lg\">{keyword.name}</h3>\n                    <Badge variant={keyword.is_active ? \"success\" : \"secondary\"}>\n                      {keyword.is_active ? \"活跃\" : \"已停用\"}\n                    </Badge>\n                    {keyword.category && (\n                      <Badge variant=\"outline\">{keyword.category}</Badge>\n                    )}\n                  </div>\n\n                  {/* 描述 */}\n                  {keyword.description && (\n                    <p className=\"text-muted-foreground text-sm\">\n                      {keyword.description}\n                    </p>\n                  )}\n\n                  {/* 元信息 */}\n                  <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                    <div className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      创建于 {format(new Date(keyword.created_at), 'yyyy年MM月dd日', { locale: zhCN })}\n                    </div>\n                    {keyword.last_report_generated_at && (\n                      <div className=\"flex items-center gap-1\">\n                        <FileText className=\"h-3 w-3\" />\n                        最后报告 {format(new Date(keyword.last_report_generated_at), 'MM月dd日', { locale: zhCN })}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* 操作按钮 */}\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={() => onToggleStatus?.(keyword.id, !keyword.is_active)}\n                    title={keyword.is_active ? \"停用关键词\" : \"启用关键词\"}\n                  >\n                    {keyword.is_active ? (\n                      <PowerOff className=\"h-4 w-4\" />\n                    ) : (\n                      <Power className=\"h-4 w-4\" />\n                    )}\n                  </Button>\n                  \n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={() => onEdit?.(keyword)}\n                    title=\"编辑关键词\"\n                  >\n                    <Edit className=\"h-4 w-4\" />\n                  </Button>\n                  \n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={() => onDelete?.(keyword.id)}\n                    title=\"删除关键词\"\n                    className=\"text-destructive hover:text-destructive\"\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {filteredKeywords.length === 0 && keywords.length > 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <Search className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">没有找到匹配的关键词</h3>\n            <p className=\"text-muted-foreground\">\n              尝试调整搜索条件或过滤器\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAnBA;;;;;;;;;AA8BO,SAAS,YAAY,EAC1B,QAAQ,EACR,UAAU,KAAK,EACf,MAAM,EACN,QAAQ,EACR,cAAc,EACG;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,SAAS;IACT,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;IAE3E,QAAQ;IACR,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;QACvF,MAAM,kBAAkB,CAAC,oBAAoB,QAAQ,QAAQ,KAAK;QAClE,MAAM,gBAAgB,gBAAgB,QAAQ,SAAS;QAEvD,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;oBAAS,WAAU;8BACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mBAPZ;;;;;;;;;;IAenB;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAMlD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;0CAIjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,eAAe,YAAY;gCACpC,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;0CAE/B,eAAe,SAAS;;;;;;;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,mIAAA,CAAA,OAAI;wBAAkB,WAAW,CAAC,QAAQ,SAAS,GAAG,eAAe;kCACpE,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyB,QAAQ,IAAI;;;;;;kEACnD,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,SAAS,GAAG,YAAY;kEAC7C,QAAQ,SAAS,GAAG,OAAO;;;;;;oDAE7B,QAAQ,QAAQ,kBACf,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,QAAQ,QAAQ;;;;;;;;;;;;4CAK7C,QAAQ,WAAW,kBAClB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAKxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;4DAC3B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,UAAU,GAAG,eAAe;gEAAE,QAAQ,oJAAA,CAAA,OAAI;4DAAC;;;;;;;oDAEzE,QAAQ,wBAAwB,kBAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;4DAC1B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,wBAAwB,GAAG,UAAU;gEAAE,QAAQ,oJAAA,CAAA,OAAI;4DAAC;;;;;;;;;;;;;;;;;;;kDAO1F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB,QAAQ,EAAE,EAAE,CAAC,QAAQ,SAAS;gDAC9D,OAAO,QAAQ,SAAS,GAAG,UAAU;0DAEpC,QAAQ,SAAS,iBAChB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;yEAEpB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAIrB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,SAAS;gDACxB,OAAM;0DAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAGlB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,WAAW,QAAQ,EAAE;gDACpC,OAAM;gDACN,WAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBApEjB,QAAQ,EAAE;;;;;;;;;;YA6ExB,iBAAiB,MAAM,KAAK,KAAK,SAAS,MAAM,GAAG,mBAClD,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA5LgB;KAAA", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/vibe/%E6%8A%A5%E6%8A%A5/baobao/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useKeywords } from '@/hooks/useKeywords'\nimport { KeywordService } from '@/lib/api/keywords'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { AddKeywordDialog } from '@/components/keywords/AddKeywordDialog'\nimport { KeywordList } from '@/components/keywords/KeywordList'\nimport { TrendingUp, LogOut, User, Settings, Plus, BarChart3, Clock, Target, Sparkles } from 'lucide-react'\nimport type { CreateKeywordRequest } from '@/types/keyword'\n\nexport default function DashboardPage() {\n  const { user, signOut, loading: authLoading } = useAuth()\n  const {\n    keywords,\n    stats,\n    loading: keywordsLoading,\n    error: keywordsError,\n    createKeyword,\n    deleteKeyword,\n    toggleKeywordStatus\n  } = useKeywords()\n\n  const [showAddDialog, setShowAddDialog] = useState(false)\n  const [isCreating, setIsCreating] = useState(false)\n  const [isAddingSample, setIsAddingSample] = useState(false)\n\n  if (authLoading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TrendingUp className=\"h-8 w-8 text-brand-primary mx-auto mb-4 animate-pulse\" />\n          <p className=\"text-muted-foreground\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    }\n  }\n\n  const handleCreateKeyword = async (data: CreateKeywordRequest) => {\n    setIsCreating(true)\n    try {\n      await createKeyword(data)\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  const handleDeleteKeyword = async (keywordId: string) => {\n    if (confirm('确定要删除这个关键词吗？此操作无法撤销。')) {\n      try {\n        await deleteKeyword(keywordId)\n      } catch (error) {\n        console.error('删除关键词失败:', error)\n      }\n    }\n  }\n\n  const handleAddSampleData = async () => {\n    if (!user?.id) return\n\n    setIsAddingSample(true)\n    try {\n      await KeywordService.addSampleData(user.id)\n      // 刷新关键词列表\n      window.location.reload()\n    } catch (error) {\n      console.error('添加示例数据失败:', error)\n    } finally {\n      setIsAddingSample(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b bg-card\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-8 w-8 text-brand-primary\" />\n              <h1 className=\"text-2xl font-bold text-foreground\">报报</h1>\n              <Badge variant=\"brand\">Beta</Badge>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2\">\n                <User className=\"h-4 w-4 text-muted-foreground\" />\n                <span className=\"text-sm text-muted-foreground\">\n                  {user?.name || user?.email}\n                </span>\n              </div>\n              <Button variant=\"ghost\" size=\"icon\">\n                <Settings className=\"h-5 w-5\" />\n              </Button>\n              <Button variant=\"outline\" onClick={handleSignOut}>\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                登出\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        {/* Welcome Section */}\n        <div className=\"mb-8 flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-3xl font-bold text-foreground mb-2\">\n              欢迎回来，{user?.name || '用户'}！ 👋\n            </h2>\n            <p className=\"text-muted-foreground text-lg\">\n              这里是您的个人仪表板，管理关键词和查看趋势报告\n            </p>\n          </div>\n          <div className=\"flex gap-2\">\n            {keywords.length === 0 && (\n              <Button\n                variant=\"outline\"\n                onClick={handleAddSampleData}\n                disabled={isAddingSample}\n              >\n                <Sparkles className=\"h-4 w-4 mr-2\" />\n                {isAddingSample ? '添加中...' : '添加示例数据'}\n              </Button>\n            )}\n            <Button variant=\"brand\" onClick={() => setShowAddDialog(true)}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              添加关键词\n            </Button>\n          </div>\n        </div>\n\n        {/* 错误提示 */}\n        {keywordsError && (\n          <Card className=\"mb-6 border-destructive\">\n            <CardContent className=\"p-4\">\n              <p className=\"text-destructive text-sm\">{keywordsError}</p>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* 统计卡片 */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-muted-foreground\">总关键词</p>\n                  <p className=\"text-2xl font-bold\">{stats?.total_keywords || 0}</p>\n                </div>\n                <Target className=\"h-8 w-8 text-brand-primary\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-muted-foreground\">活跃关键词</p>\n                  <p className=\"text-2xl font-bold\">{stats?.active_keywords || 0}</p>\n                </div>\n                <TrendingUp className=\"h-8 w-8 text-success\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-muted-foreground\">生成报告</p>\n                  <p className=\"text-2xl font-bold\">{stats?.reports_generated || 0}</p>\n                </div>\n                <BarChart3 className=\"h-8 w-8 text-info\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-muted-foreground\">最后更新</p>\n                  <p className=\"text-sm font-medium\">\n                    {stats?.last_update\n                      ? new Date(stats.last_update).toLocaleDateString('zh-CN')\n                      : '暂无'\n                    }\n                  </p>\n                </div>\n                <Clock className=\"h-8 w-8 text-warning\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* 关键词列表 */}\n        <Card>\n          <CardHeader>\n            <CardTitle>我的关键词</CardTitle>\n            <CardDescription>\n              管理您关注的关键词，系统将为活跃的关键词生成趋势分析报告\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <KeywordList\n              keywords={keywords}\n              loading={keywordsLoading}\n              onDelete={handleDeleteKeyword}\n              onToggleStatus={toggleKeywordStatus}\n            />\n          </CardContent>\n        </Card>\n\n        {/* User Info Card */}\n        <div className=\"mt-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle>账户信息</CardTitle>\n              <CardDescription>您的账户详细信息</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground\">\n                      用户ID\n                    </label>\n                    <p className=\"text-sm font-mono bg-muted p-2 rounded\">\n                      {user?.id}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground\">\n                      邮箱地址\n                    </label>\n                    <p className=\"text-sm\">\n                      {user?.email}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground\">\n                      姓名\n                    </label>\n                    <p className=\"text-sm\">\n                      {user?.name || '未设置'}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-muted-foreground\">\n                      注册时间\n                    </label>\n                    <p className=\"text-sm\">\n                      {user?.created_at ? new Date(user.created_at).toLocaleDateString('zh-CN') : '未知'}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </main>\n\n      {/* 添加关键词对话框 */}\n      <AddKeywordDialog\n        isOpen={showAddDialog}\n        onClose={() => setShowAddDialog(false)}\n        onSubmit={handleCreateKeyword}\n        loading={isCreating}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAXA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACtD,MAAM,EACJ,QAAQ,EACR,KAAK,EACL,SAAS,eAAe,EACxB,OAAO,aAAa,EACpB,aAAa,EACb,aAAa,EACb,mBAAmB,EACpB,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,cAAc;QACd,IAAI;YACF,MAAM,cAAc;QACtB,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,QAAQ,yBAAyB;YACnC,IAAI;gBACF,MAAM,cAAc;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,YAAY;YAC5B;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,IAAI;QAEf,kBAAkB;QAClB,IAAI;YACF,MAAM,gIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,KAAK,EAAE;YAC1C,UAAU;YACV,OAAO,QAAQ,CAAC,MAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;;;;;;;0CAEzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ,MAAM;;;;;;;;;;;;kDAGzB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;;0DACjC,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAA0C;4CAChD,MAAM,QAAQ;4CAAK;;;;;;;kDAE3B,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,mBACnB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,iBAAiB,WAAW;;;;;;;kDAGjC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,SAAS,IAAM,iBAAiB;;0DACtD,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAOtC,+BACC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;kCAM/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAAsB,OAAO,kBAAkB;;;;;;;;;;;;0DAE9D,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKxB,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAAsB,OAAO,mBAAmB;;;;;;;;;;;;0DAE/D,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK5B,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAAsB,OAAO,qBAAqB;;;;;;;;;;;;0DAEjE,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAK3B,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAA4C;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEACV,OAAO,cACJ,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB,CAAC,WAC/C;;;;;;;;;;;;0DAIR,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOzB,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,gJAAA,CAAA,cAAW;oCACV,UAAU;oCACV,SAAS;oCACT,UAAU;oCACV,gBAAgB;;;;;;;;;;;;;;;;;kCAMtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAG7D,6LAAC;4DAAE,WAAU;sEACV,MAAM;;;;;;;;;;;;8DAGX,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAG7D,6LAAC;4DAAE,WAAU;sEACV,MAAM;;;;;;;;;;;;8DAGX,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAG7D,6LAAC;4DAAE,WAAU;sEACV,MAAM,QAAQ;;;;;;;;;;;;8DAGnB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA4C;;;;;;sEAG7D,6LAAC;4DAAE,WAAU;sEACV,MAAM,aAAa,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5F,6LAAC,qJAAA,CAAA,mBAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,UAAU;gBACV,SAAS;;;;;;;;;;;;AAIjB;GA9QwB;;QAC0B,kIAAA,CAAA,UAAO;QASnD,8HAAA,CAAA,cAAW;;;KAVO", "debugId": null}}]}